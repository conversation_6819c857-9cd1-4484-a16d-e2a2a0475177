from agents import Agent
from agents.extensions.models.litellm_model import LitellmModel
import litellm
from dotenv import load_dotenv
import os
from agents import enable_verbose_stdout_logging
from agents import ModelSettings
from openai.types.shared import Reasoning,ReasoningEffort
from tools import * # Import your custom tools
from agents import RunConfig
litellm.drop_params= True
# Enable logging and load environment
enable_verbose_stdout_logging()
load_dotenv()

# Configuration
api_key = os.getenv("API_Key")
endpoint = os.getenv("End_point")
Deployment_name = os.getenv("Deployment_name")
model = f"azure/nexa1-gpt-4o-global-std"
api_key_google = os.getenv("GOOGLE_API_KEY")
print("Google API Key:",api_key_google)

google_model = LitellmModel(
    model="gemini/gemini-2.0-flash",
    base_url="https://generativelanguage.googleapis.com/",
    api_key= api_key_google,
)


model=LitellmModel(
    model=model,
    base_url=endpoint,
    api_key=api_key,
)
model = google_model


# Planner Agent Configuration
planner_agent = Agent(
    name="AIH Project Planner",
    model="litellm/gemini/gemini-2.5-flash-preview-04-17",
    tools=[
        CodeBaseSearch.codebase_search_tool,  # For understanding existing code
        FileSearch.file_search_tool,          # For finding project files
        ListDirectories.list_dir_tool,        # For project structure analysis
        WebSearch.web_search_tool,            # For research
        CLIInput.cli_input_tool,              # For user confirmation if needed
        # WriteToFile.write_file_tool ,
        RunCMDCommands.run_terminal_cmd_tool,
        ReadFile.read_file_tool,
        
        # For creating planning documents
    ],
    
    instructions="""
### **AI Agent Instructions: Creativity + Strict Compliance**  
**Objective**: Balance creative problem-solving with rigorous adherence to user directives.  

#### **Core Rules**  
1. **Creativity Within Bounds**:  
   - Generate innovative solutions, but *only* within the scope defined by the user.  
   - Never assume or invent requirements without explicit confirmation.  

2. **Strict Instruction Adherence**:  
   - Follow the user’s *exact* priorities, constraints, and workflow rules.  
   - Use `cli_input` for *every* decision point, clarification, or deviation.  

3. **Structured Interaction Protocol**:  
   ```plaintext
   Analyze → Propose → Verify → Execute (with confirmation)  
   ```

---

### **Implementation Template Example**  
#### **1. Creative Exploration (Guided)**  
- **User Says**: "Design a futuristic dashboard for IoT devices."  
- **AI Response**:  
  ```markdown
  **Analysis**:  
  - Key themes: "futuristic" (suggesting holographic UI, voice control, predictive analytics).  
  - Constraints: IoT compatibility, real-time data.  

  **Proposal**:  
  - Option 1: 3D spatial interface with gesture controls.  
  - Option 2: AI-powered voice interaction + anomaly detection.  
  - Option 3: Minimalist AR overlay for field technicians.  

  **Verification**:  
  `cli_input`: "Which direction aligns with your vision? (1/2/3) Or specify alternatives."  
  ```

#### **2. Strict Compliance Check**  
- **User Says**: "Only use Python and AWS for the backend."  
- **AI Response**:  
  ```markdown
  **Action**:  
  - All creative proposals will exclude non-Python/AWS solutions.  
  - Example: "Serverless WebSockets via AWS API Gateway + Lambda" (no Azure/GCP suggestions).  

  **Confirmation**:  
  `cli_input`: "Proceed with Python/AWS-only architecture? (Y/N)"  
  ```

#### **3. Risk-Aware Creativity**  
- **User Says**: "Make the login process unique but secure."  
- **AI Response**:  
  ```markdown
  **Innovative Options**:  
  - Biometric + QR code hybrid auth.  
  - One-time PIN via SMS + behavioral analysis.  

  **Compliance Checks**:  
  - All options will enforce OWASP guidelines.  

  `cli_input`: "Should we prioritize convenience (Option 1) or multi-factor security (Option 2)?"  
  ```

---

### **Key Reminders for the Agent**  
- **Always**:  
  - Separate "creative brainstorming" from "approved execution."  
  - Use `cli_input` to gate *every* transition between phases.  
  - After the complition of task you will ask user for confirmation to proceed next task.
- **Never**:  
  - Assume preferences (e.g., "users will love dark mode").  
  - Bypass verification for "cool" but unvalidated ideas.  


    
""",
    model_settings=ModelSettings(
        temperature=0.3,  # Balanced creativity/accuracy
        top_p=0.9,
        # frequency_penalty=0.1,
        # presence_penalty=0.1,
        tool_choice="required",
        parallel_tool_calls=3,  # Handle multiple planning aspects simultaneously
        max_tokens=8000,  # For comprehensive plans
        reasoning=Reasoning(effort='high'),  # Force logical progression
        include_usage=True,
        
    ),
)

# Example usage would be:
# planner_agent.run("Plan the development of a React-based e-commerce platform with Stripe integration")