import shutil
import subprocess
# Try to find full path to the editor (code)
editor_path = shutil.which("code")
if not editor_path:
    raise FileNotFoundError("VS Code CLI ('code') not found in PATH.")
old_path = r"C:\Users\<USER>\OneDrive\Desktop\<PERSON><PERSON><PERSON>kumar\forks\nseit_code_agent\requirements.txt"
new_path = r"C:\Users\<USER>\OneDrive\Desktop\Akeshkumar\forks\nseit_code_agent\requirements.txt"
command = f'"{editor_path}" --diff "{old_path}" "{new_path}"'
print(f"Running: {command}")

# Important: use shell=True for Windows to interpret the full command string
result = subprocess.run(command, shell=True)

if result.returncode != 0:
    raise RuntimeError(f"Editor launch failed with code {result.returncode}")

def 