from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any, List
import os
from fuzzywuzzy import fuzz, process
from pathlib import Path

class FileSearchArgs(BaseModel):
    query: str = Field(..., description="Fuzzy filename to search for.")
    root_dir: str = Field(
        ".", 
        description="Root directory to search from (default: current directory)"
    )
    limit: int = Field(
        10,
        description="Maximum number of results to return (default: 10)",
        gt=0,
        le=50
    )
    min_score: int = Field(
        50,
        description="Minimum fuzzy match score (0-100, default: 50)",
        ge=0,
        le=100
    )

async def run_file_search(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Fuzzy match search on file paths, returning best matches.
    Searches both filename and path components.
    """
    parsed = FileSearchArgs.model_validate_json(args)
    query = parsed.query
    root_dir = parsed.root_dir
    limit = parsed.limit
    min_score = parsed.min_score

    try:
        # Get all files recursively
        file_paths = []
        for root, _, files in os.walk(root_dir):
            for file in files:
                file_paths.append(str(Path(root) / file))

        if not file_paths:
            return f"No files found in directory: {root_dir}"

        # Extract just filenames for matching
        filenames = [Path(p).name for p in file_paths]

        # Get fuzzy matches for both full paths and filenames
        path_matches = process.extractBests(
            query,
            file_paths,
            scorer=fuzz.token_set_ratio,
            limit=limit,
            score_cutoff=min_score
        )
        name_matches = process.extractBests(
            query,
            filenames,
            scorer=fuzz.token_set_ratio,
            limit=limit,
            score_cutoff=min_score
        )

        # Combine and deduplicate results
        all_matches = {}
        for match in path_matches + name_matches:
            path, score = match
            if path not in all_matches or score > all_matches[path]:
                all_matches[path] = score

        # Sort by score (descending)
        sorted_matches = sorted(
            all_matches.items(),
            key=lambda x: x[1],
            reverse=True
        )[:limit]

        if not sorted_matches:
            return f"No files matched '{query}' with score ≥ {min_score}"

        # Format results
        result = f"Top {len(sorted_matches)} matches for '{query}':\n"
        for i, (path, score) in enumerate(sorted_matches, 1):
            result += f"{i}. {path} (score: {score})\n"

        return result.strip()

    except Exception as e:
        return f"Error during file search: {e}"

file_search_tool = FunctionTool(
    name="file_search",
    description=(
        "Fuzzy match search on file paths using filename and path components. "
        "Returns best matches with similarity scores. "
        "Searches recursively from given directory."
    ),
    params_json_schema=FileSearchArgs.model_json_schema(),
    on_invoke_tool=run_file_search,
)

