from agents import Agent,Runner
from agents.extensions.models.litellm_model import LitellmModel,LitellmConverter
from agents import ModelSettings
# import tools.FileAnalysis
# import tools.CodeBaseSearch
# import tools.DiffGen
# import tools.FileEdit
from dotenv import load_dotenv;
load_dotenv()
import os
from tools import *
api_key = os.getenv("API_Key")
endpoint = os.getenv("End_point")
Deployment_name = os.getenv("Deployment_name")
model = f"azure/gpt-4.1-mini-glo-std"

model=LitellmModel(
        model=model,
        base_url=endpoint,
        api_key=api_key,
        
    )

file_editor_agent = Agent(
    name="File Editor Agent",
    instructions="""You are an expert code editor. Follow this workflow:
    1. Read files to understand their structure
    2. When editing, always:
       a) First generate diffs to preview changes
       b) Make minimal, precise edits
       c) Preserve surrounding code context
    3. Only edit what's necessary
    4. Verify changes after editing, File should not wrongly editted.
    5. No extra edits beyond what's initially requested should be done.

    
    Constraints:
    1. Do not edit files that are not related to the task
    2. Do not edit files that are not in the codebase
    3. Do not edit files that are not in the project directory
    4. Do not edit files that are not in the project repository
    5. Do not edit files that are not in the project scope
    6. You will not write anyting in the file that is not related to the task 
    7. You will not use target file as scratch pad.
    8. You not required to create bakup file each time. When file is empity, you will not create backup file.  
    You will never work out of the scope of your capabilities and instructions. 
    """,
    tools=[FileEdit.file_edit_tool,DiffGen.diff_tool,ReadFile.read_file_tool,ReApply.reapply_tool],
    model=model  # Replace with your model,
    ,model_settings=ModelSettings(
        temperature=0.2,  # Lower for precise editing
        top_p=0.3,
        frequency_penalty=0.1,
        presence_penalty=0.1,
        tool_choice="required",
        parallel_tool_calls=3,
        max_tokens=4000,  # For comprehensive editing tasks
        # reasoning=Reasoning(effort='high'),  # Force logical progression

))