import pytest
from httpx import Async<PERSON><PERSON>
from fastapi import <PERSON>AP<PERSON>
from app.main import app
from app.core.security import get_password_hash
from app.db.database import get_database, get_sync_database
from pymongo import MongoClient
from fastapi.security import OAuth2PasswordRequestForm


@pytest.fixture
async def async_client() -> AsyncClient:
    async with Async<PERSON>lient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture(scope="module")
def test_db():
    # Use a separate database for testing
    sync_db = get_sync_database()
    # Create a test user
    username = "testuser"
    password = "testpassword"
    hashed_password = get_password_hash(password)
    user_data = {"username": username, "email": "<EMAIL>", "password": hashed_password}
    sync_db.users.insert_one(user_data)

    yield

    # Clean up the test data after tests are finished
    sync_db.users.delete_one({"username": username})


@pytest.mark.asyncio
async def test_register_user(async_client: AsyncClient, test_db):
    response = await async_client.post("/users/register", json={"username": "newuser", "email": "<EMAIL>", "password": "newpassword"})
    assert response.status_code == 200
    assert response.json()["username"] == "newuser"


@pytest.mark.asyncio
async def test_login_user(async_client: AsyncClient, test_db):
    form_data = {"username": "testuser", "password": "testpassword"}
    response = await async_client.post("/auth/login", data=form_data)
    assert response.status_code == 200
    assert "access_token" in response.json()


@pytest.mark.asyncio
async def test_protected_route(async_client: AsyncClient, test_db):
    # First, log in to get the access token
    form_data = {"username": "testuser", "password": "testpassword"}
    login_response = await async_client.post("/auth/login", data=form_data)
    access_token = login_response.json()["access_token"]

    # Then, use the access token to access the protected route
    response = await async_client.get("/items/items/", headers={"Authorization": f"Bearer {access_token}"})
    assert response.status_code == 200
    assert response.json()[0]["owner"] == "testuser"
