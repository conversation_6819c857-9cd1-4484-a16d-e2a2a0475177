# ChatGoogleGenerativeAI | 🦜️🔗 LangChain

**Source:** https://python.langchain.com/docs/integrations/chat/google_generative_ai/

[Skip to main content](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#__docusaurus_skipToContent_fallback)
**We are growing and hiring for multiple roles for Lang<PERSON><PERSON><PERSON>, LangGraph and LangSmith.[ Join our team!](https://www.langchain.com/careers)**
[![🦜️🔗 LangChain](https://python.langchain.com/img/brand/wordmark.png)![🦜️🔗 <PERSON>hain](https://python.langchain.com/img/brand/wordmark-dark.png)](https://python.langchain.com/)[Integrations](https://python.langchain.com/docs/integrations/providers/)[API Reference](https://python.langchain.com/api_reference/)
[More](https://python.langchain.com/docs/integrations/chat/google_generative_ai/)
  * [Contributing](https://python.langchain.com/docs/contributing/)
  * [People](https://python.langchain.com/docs/people/)
  * [Error reference](https://python.langchain.com/docs/troubleshooting/errors/)
  * [LangSmith](https://docs.smith.langchain.com)
  * [LangGraph](https://langchain-ai.github.io/langgraph/)
  * [LangChain Hub](https://smith.langchain.com/hub)
  * [LangChain JS/TS](https://js.langchain.com)


[v0.3](https://python.langchain.com/docs/integrations/chat/google_generative_ai/)
  * [v0.3](https://python.langchain.com/docs/introduction/)
  * [v0.2](https://python.langchain.com/v0.2/docs/introduction)
  * [v0.1](https://python.langchain.com/v0.1/docs/get_started/introduction)


[💬](https://chat.langchain.com)[](https://github.com/langchain-ai/langchain)
Search
  * [Providers](https://python.langchain.com/docs/integrations/providers/)
    * [Anthropic](https://python.langchain.com/docs/integrations/providers/anthropic/)
    * [AWS](https://python.langchain.com/docs/integrations/providers/aws/)
    * [Google](https://python.langchain.com/docs/integrations/providers/google/)
    * [Hugging Face](https://python.langchain.com/docs/integrations/providers/huggingface/)
    * [Microsoft](https://python.langchain.com/docs/integrations/providers/microsoft/)
    * [OpenAI](https://python.langchain.com/docs/integrations/providers/openai/)
    * [More](https://python.langchain.com/docs/integrations/providers/all/)
      * [Providers](https://python.langchain.com/docs/integrations/providers/)
      * [Abso](https://python.langchain.com/docs/integrations/providers/abso/)
      * [Acreom](https://python.langchain.com/docs/integrations/providers/acreom/)
      * [Activeloop Deep Lake](https://python.langchain.com/docs/integrations/providers/activeloop_deeplake/)
      * [ADS4GPTs](https://python.langchain.com/docs/integrations/providers/ads4gpts/)
      * [Aerospike](https://python.langchain.com/docs/integrations/providers/aerospike/)
      * [AgentQL](https://python.langchain.com/docs/integrations/providers/agentql/)
      * [AI21 Labs](https://python.langchain.com/docs/integrations/providers/ai21/)
      * [Aim](https://python.langchain.com/docs/integrations/providers/aim_tracking/)
      * [AINetwork](https://python.langchain.com/docs/integrations/providers/ainetwork/)
      * [Airbyte](https://python.langchain.com/docs/integrations/providers/airbyte/)
      * [Airtable](https://python.langchain.com/docs/integrations/providers/airtable/)
      * [Alchemy](https://python.langchain.com/docs/integrations/providers/alchemy/)
      * [Aleph Alpha](https://python.langchain.com/docs/integrations/providers/aleph_alpha/)
      * [Alibaba Cloud](https://python.langchain.com/docs/integrations/providers/alibaba_cloud/)
      * [AnalyticDB](https://python.langchain.com/docs/integrations/providers/analyticdb/)
      * [Annoy](https://python.langchain.com/docs/integrations/providers/annoy/)
      * [Anthropic](https://python.langchain.com/docs/integrations/providers/anthropic/)
      * [Anyscale](https://python.langchain.com/docs/integrations/providers/anyscale/)
      * [Apache Software Foundation](https://python.langchain.com/docs/integrations/providers/apache/)
      * [Apache Doris](https://python.langchain.com/docs/integrations/providers/apache_doris/)
      * [Apify](https://python.langchain.com/docs/integrations/providers/apify/)
      * [Apple](https://python.langchain.com/docs/integrations/providers/apple/)
      * [ArangoDB](https://python.langchain.com/docs/integrations/providers/arangodb/)
      * [Arcee](https://python.langchain.com/docs/integrations/providers/arcee/)
      * [ArcGIS](https://python.langchain.com/docs/integrations/providers/arcgis/)
      * [Argilla](https://python.langchain.com/docs/integrations/providers/argilla/)
      * [Arize](https://python.langchain.com/docs/integrations/providers/arize/)
      * [Arthur](https://python.langchain.com/docs/integrations/providers/arthur_tracking/)
      * [Arxiv](https://python.langchain.com/docs/integrations/providers/arxiv/)
      * [Ascend](https://python.langchain.com/docs/integrations/providers/ascend/)
      * [AskNews](https://python.langchain.com/docs/integrations/providers/asknews/)
      * [AssemblyAI](https://python.langchain.com/docs/integrations/providers/assemblyai/)
      * [Astra DB](https://python.langchain.com/docs/integrations/providers/astradb/)
      * [Atlas](https://python.langchain.com/docs/integrations/providers/atlas/)
      * [AwaDB](https://python.langchain.com/docs/integrations/providers/awadb/)
      * [AWS](https://python.langchain.com/docs/integrations/providers/aws/)
      * [AZLyrics](https://python.langchain.com/docs/integrations/providers/azlyrics/)
      * [Azure AI](https://python.langchain.com/docs/integrations/providers/azure_ai/)
      * [BAAI](https://python.langchain.com/docs/integrations/providers/baai/)
      * [Bagel](https://python.langchain.com/docs/integrations/providers/bagel/)
      * [BagelDB](https://python.langchain.com/docs/integrations/providers/bageldb/)
      * [Baichuan](https://python.langchain.com/docs/integrations/providers/baichuan/)
      * [Baidu](https://python.langchain.com/docs/integrations/providers/baidu/)
      * [Banana](https://python.langchain.com/docs/integrations/providers/bananadev/)
      * [Baseten](https://python.langchain.com/docs/integrations/providers/baseten/)
      * [Beam](https://python.langchain.com/docs/integrations/providers/beam/)
      * [Beautiful Soup](https://python.langchain.com/docs/integrations/providers/beautiful_soup/)
      * [BibTeX](https://python.langchain.com/docs/integrations/providers/bibtex/)
      * [BiliBili](https://python.langchain.com/docs/integrations/providers/bilibili/)
      * [Bittensor](https://python.langchain.com/docs/integrations/providers/bittensor/)
      * [Blackboard](https://python.langchain.com/docs/integrations/providers/blackboard/)
      * [bookend.ai](https://python.langchain.com/docs/integrations/providers/bookendai/)
      * [Box](https://python.langchain.com/docs/integrations/providers/box/)
      * [Brave Search](https://python.langchain.com/docs/integrations/providers/brave_search/)
      * [Breebs (Open Knowledge)](https://python.langchain.com/docs/integrations/providers/breebs/)
      * [Bright Data](https://python.langchain.com/docs/integrations/providers/brightdata/)
      * [Browserbase](https://python.langchain.com/docs/integrations/providers/browserbase/)
      * [Browserless](https://python.langchain.com/docs/integrations/providers/browserless/)
      * [ByteDance](https://python.langchain.com/docs/integrations/providers/byte_dance/)
      * [Cassandra](https://python.langchain.com/docs/integrations/providers/cassandra/)
      * [Cerebras](https://python.langchain.com/docs/integrations/providers/cerebras/)
      * [CerebriumAI](https://python.langchain.com/docs/integrations/providers/cerebriumai/)
      * [Chaindesk](https://python.langchain.com/docs/integrations/providers/chaindesk/)
      * [Chroma](https://python.langchain.com/docs/integrations/providers/chroma/)
      * [Clarifai](https://python.langchain.com/docs/integrations/providers/clarifai/)
      * [ClearML](https://python.langchain.com/docs/integrations/providers/clearml_tracking/)
      * [ClickHouse](https://python.langchain.com/docs/integrations/providers/clickhouse/)
      * [ClickUp](https://python.langchain.com/docs/integrations/providers/clickup/)
      * [Cloudflare](https://python.langchain.com/docs/integrations/providers/cloudflare/)
      * [Clova](https://python.langchain.com/docs/integrations/providers/clova/)
      * [CnosDB](https://python.langchain.com/docs/integrations/providers/cnosdb/)
      * [Cognee](https://python.langchain.com/docs/integrations/providers/cognee/)
      * [CogniSwitch](https://python.langchain.com/docs/integrations/providers/cogniswitch/)
      * [Cohere](https://python.langchain.com/docs/integrations/providers/cohere/)
      * [College Confidential](https://python.langchain.com/docs/integrations/providers/college_confidential/)
      * [Comet](https://python.langchain.com/docs/integrations/providers/comet_tracking/)
      * [Confident AI](https://python.langchain.com/docs/integrations/providers/confident/)
      * [Confluence](https://python.langchain.com/docs/integrations/providers/confluence/)
      * [Connery](https://python.langchain.com/docs/integrations/providers/connery/)
      * [Context](https://python.langchain.com/docs/integrations/providers/context/)
      * [Contextual AI](https://python.langchain.com/docs/integrations/providers/contextual/)
      * [Couchbase](https://python.langchain.com/docs/integrations/providers/couchbase/)
      * [Coze](https://python.langchain.com/docs/integrations/providers/coze/)
      * [CrateDB](https://python.langchain.com/docs/integrations/providers/cratedb/)
      * [C Transformers](https://python.langchain.com/docs/integrations/providers/ctransformers/)
      * [CTranslate2](https://python.langchain.com/docs/integrations/providers/ctranslate2/)
      * [Cube](https://python.langchain.com/docs/integrations/providers/cube/)
      * [Dappier](https://python.langchain.com/docs/integrations/providers/dappier/)
      * [DashVector](https://python.langchain.com/docs/integrations/providers/dashvector/)
      * [Databricks](https://python.langchain.com/docs/integrations/providers/databricks/)
      * [Datadog Tracing](https://python.langchain.com/docs/integrations/providers/datadog/)
      * [Datadog Logs](https://python.langchain.com/docs/integrations/providers/datadog_logs/)
      * [DataForSEO](https://python.langchain.com/docs/integrations/providers/dataforseo/)
      * [Dataherald](https://python.langchain.com/docs/integrations/providers/dataherald/)
      * [Dedoc](https://python.langchain.com/docs/integrations/providers/dedoc/)
      * [DeepInfra](https://python.langchain.com/docs/integrations/providers/deepinfra/)
      * [Deeplake](https://python.langchain.com/docs/integrations/providers/deeplake/)
      * [DeepSeek](https://python.langchain.com/docs/integrations/providers/deepseek/)
      * [DeepSparse](https://python.langchain.com/docs/integrations/providers/deepsparse/)
      * [Dell](https://python.langchain.com/docs/integrations/providers/dell/)
      * [Diffbot](https://python.langchain.com/docs/integrations/providers/diffbot/)
      * [DingoDB](https://python.langchain.com/docs/integrations/providers/dingo/)
      * [Discord](https://python.langchain.com/docs/integrations/providers/discord-shikenso/)
      * [Discord (community loader)](https://python.langchain.com/docs/integrations/providers/discord/)
      * [DocArray](https://python.langchain.com/docs/integrations/providers/docarray/)
      * [Docling](https://python.langchain.com/docs/integrations/providers/docling/)
      * [Doctran](https://python.langchain.com/docs/integrations/providers/doctran/)
      * [Docugami](https://python.langchain.com/docs/integrations/providers/docugami/)
      * [Docusaurus](https://python.langchain.com/docs/integrations/providers/docusaurus/)
      * [Dria](https://python.langchain.com/docs/integrations/providers/dria/)
      * [Dropbox](https://python.langchain.com/docs/integrations/providers/dropbox/)
      * [DuckDB](https://python.langchain.com/docs/integrations/providers/duckdb/)
      * [DuckDuckGo Search](https://python.langchain.com/docs/integrations/providers/duckduckgo_search/)
      * [E2B](https://python.langchain.com/docs/integrations/providers/e2b/)
      * [Eden AI](https://python.langchain.com/docs/integrations/providers/edenai/)
      * [Elasticsearch](https://python.langchain.com/docs/integrations/providers/elasticsearch/)
      * [ElevenLabs](https://python.langchain.com/docs/integrations/providers/elevenlabs/)
      * [Embedchain](https://python.langchain.com/docs/integrations/providers/embedchain/)
      * [Epsilla](https://python.langchain.com/docs/integrations/providers/epsilla/)
      * [Etherscan](https://python.langchain.com/docs/integrations/providers/etherscan/)
      * [Everly AI](https://python.langchain.com/docs/integrations/providers/everlyai/)
      * [EverNote](https://python.langchain.com/docs/integrations/providers/evernote/)
      * [Exa](https://python.langchain.com/docs/integrations/providers/exa_search/)
      * [Facebook - Meta](https://python.langchain.com/docs/integrations/providers/facebook/)
      * [FalkorDB](https://python.langchain.com/docs/integrations/providers/falkordb/)
      * [Fauna](https://python.langchain.com/docs/integrations/providers/fauna/)
      * [Featherless AI](https://python.langchain.com/docs/integrations/providers/featherless-ai/)
      * [Fiddler](https://python.langchain.com/docs/integrations/providers/fiddler/)
      * [Figma](https://python.langchain.com/docs/integrations/providers/figma/)
      * [FireCrawl](https://python.langchain.com/docs/integrations/providers/firecrawl/)
      * [Fireworks AI](https://python.langchain.com/docs/integrations/providers/fireworks/)
      * [Flyte](https://python.langchain.com/docs/integrations/providers/flyte/)
      * [FMP Data (Financial Data Prep)](https://python.langchain.com/docs/integrations/providers/fmp-data/)
      * [Forefront AI](https://python.langchain.com/docs/integrations/providers/forefrontai/)
      * [Friendli AI](https://python.langchain.com/docs/integrations/providers/friendli/)
      * [Smabbler](https://python.langchain.com/docs/integrations/providers/galaxia/)
      * [Gel](https://python.langchain.com/docs/integrations/providers/gel/)
      * [Geopandas](https://python.langchain.com/docs/integrations/providers/geopandas/)
      * [Git](https://python.langchain.com/docs/integrations/providers/git/)
      * [GitBook](https://python.langchain.com/docs/integrations/providers/gitbook/)
      * [GitHub](https://python.langchain.com/docs/integrations/providers/github/)
      * [GitLab](https://python.langchain.com/docs/integrations/providers/gitlab/)
      * [GOAT](https://python.langchain.com/docs/integrations/providers/goat/)
      * [Golden](https://python.langchain.com/docs/integrations/providers/golden/)
      * [Goodfire](https://python.langchain.com/docs/integrations/providers/goodfire/)
      * [Google](https://python.langchain.com/docs/integrations/providers/google/)
      * [Serper - Google Search API](https://python.langchain.com/docs/integrations/providers/google_serper/)
      * [GooseAI](https://python.langchain.com/docs/integrations/providers/gooseai/)
      * [GPT4All](https://python.langchain.com/docs/integrations/providers/gpt4all/)
      * [Gradient](https://python.langchain.com/docs/integrations/providers/gradient/)
      * [Graph RAG](https://python.langchain.com/docs/integrations/providers/graph_rag/)
      * [Graphsignal](https://python.langchain.com/docs/integrations/providers/graphsignal/)
      * [Grobid](https://python.langchain.com/docs/integrations/providers/grobid/)
      * [Groq](https://python.langchain.com/docs/integrations/providers/groq/)
      * [Gutenberg](https://python.langchain.com/docs/integrations/providers/gutenberg/)
      * [Hacker News](https://python.langchain.com/docs/integrations/providers/hacker_news/)
      * [Hazy Research](https://python.langchain.com/docs/integrations/providers/hazy_research/)
      * [Helicone](https://python.langchain.com/docs/integrations/providers/helicone/)
      * [Hologres](https://python.langchain.com/docs/integrations/providers/hologres/)
      * [HTML to text](https://python.langchain.com/docs/integrations/providers/html2text/)
      * [Huawei](https://python.langchain.com/docs/integrations/providers/huawei/)
      * [Hugging Face](https://python.langchain.com/docs/integrations/providers/huggingface/)
      * [Hyperbrowser](https://python.langchain.com/docs/integrations/providers/hyperbrowser/)
      * [IBM](https://python.langchain.com/docs/integrations/providers/ibm/)
      * [IEIT Systems](https://python.langchain.com/docs/integrations/providers/ieit_systems/)
      * [iFixit](https://python.langchain.com/docs/integrations/providers/ifixit/)
      * [iFlytek](https://python.langchain.com/docs/integrations/providers/iflytek/)
      * [IMSDb](https://python.langchain.com/docs/integrations/providers/imsdb/)
      * [Infinispan VS](https://python.langchain.com/docs/integrations/providers/infinispanvs/)
      * [Infinity](https://python.langchain.com/docs/integrations/providers/infinity/)
      * [Infino](https://python.langchain.com/docs/integrations/providers/infino/)
      * [Intel](https://python.langchain.com/docs/integrations/providers/intel/)
      * [Iugu](https://python.langchain.com/docs/integrations/providers/iugu/)
      * [Jaguar](https://python.langchain.com/docs/integrations/providers/jaguar/)
      * [Javelin AI Gateway](https://python.langchain.com/docs/integrations/providers/javelin_ai_gateway/)
      * [Jenkins](https://python.langchain.com/docs/integrations/providers/jenkins/)
      * [Jina AI](https://python.langchain.com/docs/integrations/providers/jina/)
      * [Johnsnowlabs](https://python.langchain.com/docs/integrations/providers/johnsnowlabs/)
      * [Joplin](https://python.langchain.com/docs/integrations/providers/joplin/)
      * [KDB.AI](https://python.langchain.com/docs/integrations/providers/kdbai/)
      * [Kinetica](https://python.langchain.com/docs/integrations/providers/kinetica/)
      * [KoboldAI](https://python.langchain.com/docs/integrations/providers/koboldai/)
      * [Konko](https://python.langchain.com/docs/integrations/providers/konko/)
      * [KoNLPY](https://python.langchain.com/docs/integrations/providers/konlpy/)
      * [Kùzu](https://python.langchain.com/docs/integrations/providers/kuzu/)
      * [Label Studio](https://python.langchain.com/docs/integrations/providers/labelstudio/)
      * [lakeFS](https://python.langchain.com/docs/integrations/providers/lakefs/)
      * [LanceDB](https://python.langchain.com/docs/integrations/providers/lancedb/)
      * [LangChain Decorators ✨](https://python.langchain.com/docs/integrations/providers/langchain_decorators/)
      * [LangFair: Use-Case Level LLM Bias and Fairness Assessments](https://python.langchain.com/docs/integrations/providers/langfair/)
      * [Langfuse 🪢](https://python.langchain.com/docs/integrations/providers/langfuse/)
      * [Lantern](https://python.langchain.com/docs/integrations/providers/lantern/)
      * [Lindorm](https://python.langchain.com/docs/integrations/providers/lindorm/)
      * [Linkup](https://python.langchain.com/docs/integrations/providers/linkup/)
      * [LiteLLM](https://python.langchain.com/docs/integrations/providers/litellm/)
      * [LlamaIndex](https://python.langchain.com/docs/integrations/providers/llama_index/)
      * [Llama.cpp](https://python.langchain.com/docs/integrations/providers/llamacpp/)
      * [LlamaEdge](https://python.langchain.com/docs/integrations/providers/llamaedge/)
      * [llamafile](https://python.langchain.com/docs/integrations/providers/llamafile/)
      * [LLMonitor](https://python.langchain.com/docs/integrations/providers/llmonitor/)
      * [LocalAI](https://python.langchain.com/docs/integrations/providers/localai/)
      * [Log10](https://python.langchain.com/docs/integrations/providers/log10/)
      * [MariaDB](https://python.langchain.com/docs/integrations/providers/mariadb/)
      * [MariTalk](https://python.langchain.com/docs/integrations/providers/maritalk/)
      * [Marqo](https://python.langchain.com/docs/integrations/providers/marqo/)
      * [MediaWikiDump](https://python.langchain.com/docs/integrations/providers/mediawikidump/)
      * [Meilisearch](https://python.langchain.com/docs/integrations/providers/meilisearch/)
      * [Memcached](https://python.langchain.com/docs/integrations/providers/memcached/)
      * [Memgraph](https://python.langchain.com/docs/integrations/providers/memgraph/)
      * [Metal](https://python.langchain.com/docs/integrations/providers/metal/)
      * [Microsoft](https://python.langchain.com/docs/integrations/providers/microsoft/)
      * [Milvus](https://python.langchain.com/docs/integrations/providers/milvus/)
      * [MindsDB](https://python.langchain.com/docs/integrations/providers/mindsdb/)
      * [Minimax](https://python.langchain.com/docs/integrations/providers/minimax/)
      * [MistralAI](https://python.langchain.com/docs/integrations/providers/mistralai/)
      * [MLflow AI Gateway for LLMs](https://python.langchain.com/docs/integrations/providers/mlflow/)
      * [MLflow](https://python.langchain.com/docs/integrations/providers/mlflow_tracking/)
      * [MLX](https://python.langchain.com/docs/integrations/providers/mlx/)
      * [Modal](https://python.langchain.com/docs/integrations/providers/modal/)
      * [ModelScope](https://python.langchain.com/docs/integrations/providers/modelscope/)
      * [Modern Treasury](https://python.langchain.com/docs/integrations/providers/modern_treasury/)
      * [Momento](https://python.langchain.com/docs/integrations/providers/momento/)
      * [MongoDB](https://python.langchain.com/docs/integrations/providers/mongodb/)
      * [MongoDB Atlas](https://python.langchain.com/docs/integrations/providers/mongodb_atlas/)
      * [Motherduck](https://python.langchain.com/docs/integrations/providers/motherduck/)
      * [Motörhead](https://python.langchain.com/docs/integrations/providers/motorhead/)
      * [MyScale](https://python.langchain.com/docs/integrations/providers/myscale/)
      * [NAVER](https://python.langchain.com/docs/integrations/providers/naver/)
      * [Nebius](https://python.langchain.com/docs/integrations/providers/nebius/)
      * [Neo4j](https://python.langchain.com/docs/integrations/providers/neo4j/)
      * [Netmind](https://python.langchain.com/docs/integrations/providers/netmind/)
      * [Nimble](https://python.langchain.com/docs/integrations/providers/nimble/)
      * [NLPCloud](https://python.langchain.com/docs/integrations/providers/nlpcloud/)
      * [Nomic](https://python.langchain.com/docs/integrations/providers/nomic/)
      * [Notion DB](https://python.langchain.com/docs/integrations/providers/notion/)
      * [Nuclia](https://python.langchain.com/docs/integrations/providers/nuclia/)
      * [NVIDIA](https://python.langchain.com/docs/integrations/providers/nvidia/)
      * [Obsidian](https://python.langchain.com/docs/integrations/providers/obsidian/)
      * [OceanBase](https://python.langchain.com/docs/integrations/providers/oceanbase/)
      * [Oracle Cloud Infrastructure (OCI)](https://python.langchain.com/docs/integrations/providers/oci/)
      * [OctoAI](https://python.langchain.com/docs/integrations/providers/octoai/)
      * [Ollama](https://python.langchain.com/docs/integrations/providers/ollama/)
      * [Ontotext GraphDB](https://python.langchain.com/docs/integrations/providers/ontotext_graphdb/)
      * [OpenAI](https://python.langchain.com/docs/integrations/providers/openai/)
      * [OpenGradient](https://python.langchain.com/docs/integrations/providers/opengradient/)
      * [OpenLLM](https://python.langchain.com/docs/integrations/providers/openllm/)
      * [OpenSearch](https://python.langchain.com/docs/integrations/providers/opensearch/)
      * [OpenWeatherMap](https://python.langchain.com/docs/integrations/providers/openweathermap/)
      * [OracleAI Vector Search](https://python.langchain.com/docs/integrations/providers/oracleai/)
      * [Outline](https://python.langchain.com/docs/integrations/providers/outline/)
      * [Outlines](https://python.langchain.com/docs/integrations/providers/outlines/)
      * [Oxylabs](https://python.langchain.com/docs/integrations/providers/oxylabs/)
      * [Pandas](https://python.langchain.com/docs/integrations/providers/pandas/)
      * [PaymanAI](https://python.langchain.com/docs/integrations/providers/payman-tool/)
      * [Pebblo](https://python.langchain.com/docs/integrations/providers/pebblo/)
      * [Permit](https://python.langchain.com/docs/integrations/providers/permit/)
      * [Perplexity](https://python.langchain.com/docs/integrations/providers/perplexity/)
      * [Petals](https://python.langchain.com/docs/integrations/providers/petals/)
      * [Postgres Embedding](https://python.langchain.com/docs/integrations/providers/pg_embedding/)
      * [PGVector](https://python.langchain.com/docs/integrations/providers/pgvector/)
      * [Pinecone](https://python.langchain.com/docs/integrations/providers/pinecone/)
      * [PipelineAI](https://python.langchain.com/docs/integrations/providers/pipelineai/)
      * [Pipeshift](https://python.langchain.com/docs/integrations/providers/pipeshift/)
      * [Portkey](https://python.langchain.com/docs/integrations/providers/portkey/)
      * [Predibase](https://python.langchain.com/docs/integrations/providers/predibase/)
      * [Prediction Guard](https://python.langchain.com/docs/integrations/providers/predictionguard/)
      * [PremAI](https://python.langchain.com/docs/integrations/providers/premai/)
      * [SWI-Prolog](https://python.langchain.com/docs/integrations/providers/prolog/)
      * [PromptLayer](https://python.langchain.com/docs/integrations/providers/promptlayer/)
      * [Psychic](https://python.langchain.com/docs/integrations/providers/psychic/)
      * [PubMed](https://python.langchain.com/docs/integrations/providers/pubmed/)
      * [PullMd Loader](https://python.langchain.com/docs/integrations/providers/pull-md/)
      * [PygmalionAI](https://python.langchain.com/docs/integrations/providers/pygmalionai/)
      * [PyMuPDF4LLM](https://python.langchain.com/docs/integrations/providers/pymupdf4llm/)
      * [Qdrant](https://python.langchain.com/docs/integrations/providers/qdrant/)
      * [RAGatouille](https://python.langchain.com/docs/integrations/providers/ragatouille/)
      * [rank_bm25](https://python.langchain.com/docs/integrations/providers/rank_bm25/)
      * [Ray Serve](https://python.langchain.com/docs/integrations/providers/ray_serve/)
      * [Rebuff](https://python.langchain.com/docs/integrations/providers/rebuff/)
      * [Reddit](https://python.langchain.com/docs/integrations/providers/reddit/)
      * [Redis](https://python.langchain.com/docs/integrations/providers/redis/)
      * [Remembrall](https://python.langchain.com/docs/integrations/providers/remembrall/)
      * [Replicate](https://python.langchain.com/docs/integrations/providers/replicate/)
      * [Roam](https://python.langchain.com/docs/integrations/providers/roam/)
      * [Sema4 (fka Robocorp)](https://python.langchain.com/docs/integrations/providers/robocorp/)
      * [Rockset](https://python.langchain.com/docs/integrations/providers/rockset/)
      * [Runhouse](https://python.langchain.com/docs/integrations/providers/runhouse/)
      * [Runpod](https://python.langchain.com/docs/integrations/providers/runpod/)
      * [RWKV-4](https://python.langchain.com/docs/integrations/providers/rwkv/)
      * [Salesforce](https://python.langchain.com/docs/integrations/providers/salesforce/)
      * [SambaNova](https://python.langchain.com/docs/integrations/providers/sambanova/)
      * [SAP](https://python.langchain.com/docs/integrations/providers/sap/)
      * [ScrapeGraph AI](https://python.langchain.com/docs/integrations/providers/scrapegraph/)
      * [SearchApi](https://python.langchain.com/docs/integrations/providers/searchapi/)
      * [SearxNG Search API](https://python.langchain.com/docs/integrations/providers/searx/)
      * [SemaDB](https://python.langchain.com/docs/integrations/providers/semadb/)
      * [SerpAPI](https://python.langchain.com/docs/integrations/providers/serpapi/)
      * [Shale Protocol](https://python.langchain.com/docs/integrations/providers/shaleprotocol/)
      * [SingleStore Integration](https://python.langchain.com/docs/integrations/providers/singlestore/)
      * [scikit-learn](https://python.langchain.com/docs/integrations/providers/sklearn/)
      * [Slack](https://python.langchain.com/docs/integrations/providers/slack/)
      * [Snowflake](https://python.langchain.com/docs/integrations/providers/snowflake/)
      * [spaCy](https://python.langchain.com/docs/integrations/providers/spacy/)
      * [Spark](https://python.langchain.com/docs/integrations/providers/spark/)
      * [SparkLLM](https://python.langchain.com/docs/integrations/providers/sparkllm/)
      * [Spreedly](https://python.langchain.com/docs/integrations/providers/spreedly/)
      * [SQLite](https://python.langchain.com/docs/integrations/providers/sqlite/)
      * [Stack Exchange](https://python.langchain.com/docs/integrations/providers/stackexchange/)
      * [StarRocks](https://python.langchain.com/docs/integrations/providers/starrocks/)
      * [StochasticAI](https://python.langchain.com/docs/integrations/providers/stochasticai/)
      * [Streamlit](https://python.langchain.com/docs/integrations/providers/streamlit/)
      * [Stripe](https://python.langchain.com/docs/integrations/providers/stripe/)
      * [Supabase (Postgres)](https://python.langchain.com/docs/integrations/providers/supabase/)
      * [Nebula](https://python.langchain.com/docs/integrations/providers/symblai_nebula/)
      * [Tableau](https://python.langchain.com/docs/integrations/providers/tableau/)
      * [Taiga](https://python.langchain.com/docs/integrations/providers/taiga/)
      * [Tair](https://python.langchain.com/docs/integrations/providers/tair/)
      * [Tavily](https://python.langchain.com/docs/integrations/providers/tavily/)
      * [Telegram](https://python.langchain.com/docs/integrations/providers/telegram/)
      * [Tencent](https://python.langchain.com/docs/integrations/providers/tencent/)
      * [TensorFlow Datasets](https://python.langchain.com/docs/integrations/providers/tensorflow_datasets/)
      * [TiDB](https://python.langchain.com/docs/integrations/providers/tidb/)
      * [TigerGraph](https://python.langchain.com/docs/integrations/providers/tigergraph/)
      * [Tigris](https://python.langchain.com/docs/integrations/providers/tigris/)
      * [Tilores](https://python.langchain.com/docs/integrations/providers/tilores/)
      * [Together AI](https://python.langchain.com/docs/integrations/providers/together/)
      * [2Markdown](https://python.langchain.com/docs/integrations/providers/tomarkdown/)
      * [Transwarp](https://python.langchain.com/docs/integrations/providers/transwarp/)
      * [Trello](https://python.langchain.com/docs/integrations/providers/trello/)
      * [Trubrics](https://python.langchain.com/docs/integrations/providers/trubrics/)
      * [TruLens](https://python.langchain.com/docs/integrations/providers/trulens/)
      * [Twitter](https://python.langchain.com/docs/integrations/providers/twitter/)
      * [Typesense](https://python.langchain.com/docs/integrations/providers/typesense/)
      * [Unstructured](https://python.langchain.com/docs/integrations/providers/unstructured/)
      * [Upstage](https://python.langchain.com/docs/integrations/providers/upstage/)
      * [upstash](https://python.langchain.com/docs/integrations/providers/upstash/)
      * [UpTrain](https://python.langchain.com/docs/integrations/providers/uptrain/)
      * [USearch](https://python.langchain.com/docs/integrations/providers/usearch/)
      * [Valthera](https://python.langchain.com/docs/integrations/providers/valthera/)
      * [Valyu Deep Search](https://python.langchain.com/docs/integrations/providers/valyu/)
      * [VDMS](https://python.langchain.com/docs/integrations/providers/vdms/)
      * [Vearch](https://python.langchain.com/docs/integrations/providers/vearch/)
      * [Vectara](https://python.langchain.com/docs/integrations/providers/vectara/)
      * [Vectorize](https://python.langchain.com/docs/integrations/providers/vectorize/)
      * [Vespa](https://python.langchain.com/docs/integrations/providers/vespa/)
      * [vlite](https://python.langchain.com/docs/integrations/providers/vlite/)
      * [VoyageAI](https://python.langchain.com/docs/integrations/providers/voyageai/)
      * [Weights & Biases](https://python.langchain.com/docs/integrations/providers/wandb/)
      * [Weights & Biases tracing](https://python.langchain.com/docs/integrations/providers/wandb_tracing/)
      * [Weights & Biases tracking](https://python.langchain.com/docs/integrations/providers/wandb_tracking/)
      * [Weather](https://python.langchain.com/docs/integrations/providers/weather/)
      * [Weaviate](https://python.langchain.com/docs/integrations/providers/weaviate/)
      * [WhatsApp](https://python.langchain.com/docs/integrations/providers/whatsapp/)
      * [WhyLabs](https://python.langchain.com/docs/integrations/providers/whylabs_profiling/)
      * [Wikipedia](https://python.langchain.com/docs/integrations/providers/wikipedia/)
      * [Wolfram Alpha](https://python.langchain.com/docs/integrations/providers/wolfram_alpha/)
      * [Writer, Inc.](https://python.langchain.com/docs/integrations/providers/writer/)
      * [xAI](https://python.langchain.com/docs/integrations/providers/xai/)
      * [Xata](https://python.langchain.com/docs/integrations/providers/xata/)
      * [Xorbits Inference (Xinference)](https://python.langchain.com/docs/integrations/providers/xinference/)
      * [Yahoo](https://python.langchain.com/docs/integrations/providers/yahoo/)
      * [Yandex](https://python.langchain.com/docs/integrations/providers/yandex/)
      * [YDB](https://python.langchain.com/docs/integrations/providers/ydb/)
      * [Yeager.ai](https://python.langchain.com/docs/integrations/providers/yeagerai/)
      * [Yellowbrick](https://python.langchain.com/docs/integrations/providers/yellowbrick/)
      * [01.AI](https://python.langchain.com/docs/integrations/providers/yi/)
      * [You](https://python.langchain.com/docs/integrations/providers/you/)
      * [YouTube](https://python.langchain.com/docs/integrations/providers/youtube/)
      * [Zep](https://python.langchain.com/docs/integrations/providers/zep/)
      * [Zhipu AI](https://python.langchain.com/docs/integrations/providers/zhipuai/)
      * [Zilliz](https://python.langchain.com/docs/integrations/providers/zilliz/)
      * [Zotero](https://python.langchain.com/docs/integrations/providers/zotero/)
  * [Components](https://python.langchain.com/docs/integrations/components/)
    * [Chat models](https://python.langchain.com/docs/integrations/chat/)
      * [Chat models](https://python.langchain.com/docs/integrations/chat/)
      * [Abso](https://python.langchain.com/docs/integrations/chat/abso/)
      * [AI21 Labs](https://python.langchain.com/docs/integrations/chat/ai21/)
      * [Alibaba Cloud PAI EAS](https://python.langchain.com/docs/integrations/chat/alibaba_cloud_pai_eas/)
      * [Anthropic](https://python.langchain.com/docs/integrations/chat/anthropic/)
      * [[Deprecated] Experimental Anthropic Tools Wrapper](https://python.langchain.com/docs/integrations/chat/anthropic_functions/)
      * [Anyscale](https://python.langchain.com/docs/integrations/chat/anyscale/)
      * [AzureAIChatCompletionsModel](https://python.langchain.com/docs/integrations/chat/azure_ai/)
      * [Azure OpenAI](https://python.langchain.com/docs/integrations/chat/azure_chat_openai/)
      * [Azure ML Endpoint](https://python.langchain.com/docs/integrations/chat/azureml_chat_endpoint/)
      * [Baichuan Chat](https://python.langchain.com/docs/integrations/chat/baichuan/)
      * [Baidu Qianfan](https://python.langchain.com/docs/integrations/chat/baidu_qianfan_endpoint/)
      * [AWS Bedrock](https://python.langchain.com/docs/integrations/chat/bedrock/)
      * [Cerebras](https://python.langchain.com/docs/integrations/chat/cerebras/)
      * [CloudflareWorkersAI](https://python.langchain.com/docs/integrations/chat/cloudflare_workersai/)
      * [Cohere](https://python.langchain.com/docs/integrations/chat/cohere/)
      * [ContextualAI](https://python.langchain.com/docs/integrations/chat/contextual/)
      * [Coze Chat](https://python.langchain.com/docs/integrations/chat/coze/)
      * [Dappier AI](https://python.langchain.com/docs/integrations/chat/dappier/)
      * [Databricks](https://python.langchain.com/docs/integrations/chat/databricks/)
      * [DeepInfra](https://python.langchain.com/docs/integrations/chat/deepinfra/)
      * [DeepSeek](https://python.langchain.com/docs/integrations/chat/deepseek/)
      * [Eden AI](https://python.langchain.com/docs/integrations/chat/edenai/)
      * [Ernie Bot Chat](https://python.langchain.com/docs/integrations/chat/ernie/)
      * [EverlyAI](https://python.langchain.com/docs/integrations/chat/everlyai/)
      * [Featherless AI](https://python.langchain.com/docs/integrations/chat/featherless_ai/)
      * [Fireworks](https://python.langchain.com/docs/integrations/chat/fireworks/)
      * [ChatFriendli](https://python.langchain.com/docs/integrations/chat/friendli/)
      * [Goodfire](https://python.langchain.com/docs/integrations/chat/goodfire/)
      * [Google Gemini](https://python.langchain.com/docs/integrations/chat/google_generative_ai/)
      * [Google Cloud Vertex AI](https://python.langchain.com/docs/integrations/chat/google_vertex_ai_palm/)
      * [GPTRouter](https://python.langchain.com/docs/integrations/chat/gpt_router/)
      * [Groq](https://python.langchain.com/docs/integrations/chat/groq/)
      * [ChatHuggingFace](https://python.langchain.com/docs/integrations/chat/huggingface/)
      * [IBM watsonx.ai](https://python.langchain.com/docs/integrations/chat/ibm_watsonx/)
      * [JinaChat](https://python.langchain.com/docs/integrations/chat/jinachat/)
      * [Kinetica](https://python.langchain.com/docs/integrations/chat/kinetica/)
      * [Konko](https://python.langchain.com/docs/integrations/chat/konko/)
      * [LiteLLM](https://python.langchain.com/docs/integrations/chat/litellm/)
      * [Llama 2 Chat](https://python.langchain.com/docs/integrations/chat/llama2_chat/)
      * [Llama API](https://python.langchain.com/docs/integrations/chat/llama_api/)
      * [LlamaEdge](https://python.langchain.com/docs/integrations/chat/llama_edge/)
      * [Llama.cpp](https://python.langchain.com/docs/integrations/chat/llamacpp/)
      * [maritalk](https://python.langchain.com/docs/integrations/chat/maritalk/)
      * [MiniMax](https://python.langchain.com/docs/integrations/chat/minimax/)
      * [MistralAI](https://python.langchain.com/docs/integrations/chat/mistralai/)
      * [MLX](https://python.langchain.com/docs/integrations/chat/mlx/)
      * [ModelScope](https://python.langchain.com/docs/integrations/chat/modelscope_chat_endpoint/)
      * [Moonshot](https://python.langchain.com/docs/integrations/chat/moonshot/)
      * [Naver](https://python.langchain.com/docs/integrations/chat/naver/)
      * [Nebius](https://python.langchain.com/docs/integrations/chat/nebius/)
      * [Netmind](https://python.langchain.com/docs/integrations/chat/netmind/)
      * [NVIDIA AI Endpoints](https://python.langchain.com/docs/integrations/chat/nvidia_ai_endpoints/)
      * [ChatOCIModelDeployment](https://python.langchain.com/docs/integrations/chat/oci_data_science/)
      * [OCIGenAI](https://python.langchain.com/docs/integrations/chat/oci_generative_ai/)
      * [ChatOctoAI](https://python.langchain.com/docs/integrations/chat/octoai/)
      * [Ollama](https://python.langchain.com/docs/integrations/chat/ollama/)
      * [OpenAI](https://python.langchain.com/docs/integrations/chat/openai/)
      * [Outlines](https://python.langchain.com/docs/integrations/chat/outlines/)
      * [Perplexity](https://python.langchain.com/docs/integrations/chat/perplexity/)
      * [Pipeshift](https://python.langchain.com/docs/integrations/chat/pipeshift/)
      * [ChatPredictionGuard](https://python.langchain.com/docs/integrations/chat/predictionguard/)
      * [PremAI](https://python.langchain.com/docs/integrations/chat/premai/)
      * [PromptLayer ChatOpenAI](https://python.langchain.com/docs/integrations/chat/promptlayer_chatopenai/)
      * [Qwen QwQ](https://python.langchain.com/docs/integrations/chat/qwq/)
      * [Reka](https://python.langchain.com/docs/integrations/chat/reka/)
      * [RunPod Chat Model](https://python.langchain.com/docs/integrations/chat/runpod/)
      * [SambaNovaCloud](https://python.langchain.com/docs/integrations/chat/sambanova/)
      * [SambaStudio](https://python.langchain.com/docs/integrations/chat/sambastudio/)
      * [ChatSeekrFlow](https://python.langchain.com/docs/integrations/chat/seekrflow/)
      * [Snowflake Cortex](https://python.langchain.com/docs/integrations/chat/snowflake/)
      * [solar](https://python.langchain.com/docs/integrations/chat/solar/)
      * [SparkLLM Chat](https://python.langchain.com/docs/integrations/chat/sparkllm/)
      * [Nebula (Symbl.ai)](https://python.langchain.com/docs/integrations/chat/symblai_nebula/)
      * [Tencent Hunyuan](https://python.langchain.com/docs/integrations/chat/tencent_hunyuan/)
      * [Together](https://python.langchain.com/docs/integrations/chat/together/)
      * [Tongyi Qwen](https://python.langchain.com/docs/integrations/chat/tongyi/)
      * [Upstage](https://python.langchain.com/docs/integrations/chat/upstage/)
      * [vectara](https://python.langchain.com/docs/integrations/chat/vectara/)
      * [vLLM Chat](https://python.langchain.com/docs/integrations/chat/vllm/)
      * [Volc Engine Maas](https://python.langchain.com/docs/integrations/chat/volcengine_maas/)
      * [Chat Writer](https://python.langchain.com/docs/integrations/chat/writer/)
      * [xAI](https://python.langchain.com/docs/integrations/chat/xai/)
      * [Xinference](https://python.langchain.com/docs/integrations/chat/xinference/)
      * [YandexGPT](https://python.langchain.com/docs/integrations/chat/yandex/)
      * [ChatYI](https://python.langchain.com/docs/integrations/chat/yi/)
      * [Yuan2.0](https://python.langchain.com/docs/integrations/chat/yuan2/)
      * [ZHIPU AI](https://python.langchain.com/docs/integrations/chat/zhipuai/)
    * [Retrievers](https://python.langchain.com/docs/integrations/retrievers/)
      * [Retrievers](https://python.langchain.com/docs/integrations/retrievers/)
      * [Activeloop Deep Memory](https://python.langchain.com/docs/integrations/retrievers/activeloop/)
      * [Amazon Kendra](https://python.langchain.com/docs/integrations/retrievers/amazon_kendra_retriever/)
      * [Arcee](https://python.langchain.com/docs/integrations/retrievers/arcee/)
      * [Arxiv](https://python.langchain.com/docs/integrations/retrievers/arxiv/)
      * [AskNews](https://python.langchain.com/docs/integrations/retrievers/asknews/)
      * [Azure AI Search](https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/)
      * [Bedrock (Knowledge Bases)](https://python.langchain.com/docs/integrations/retrievers/bedrock/)
      * [BM25](https://python.langchain.com/docs/integrations/retrievers/bm25/)
      * [Box](https://python.langchain.com/docs/integrations/retrievers/box/)
      * [BREEBS (Open Knowledge)](https://python.langchain.com/docs/integrations/retrievers/breebs/)
      * [Chaindesk](https://python.langchain.com/docs/integrations/retrievers/chaindesk/)
      * [ChatGPT plugin](https://python.langchain.com/docs/integrations/retrievers/chatgpt-plugin/)
      * [Cognee](https://python.langchain.com/docs/integrations/retrievers/cognee/)
      * [Cohere reranker](https://python.langchain.com/docs/integrations/retrievers/cohere-reranker/)
      * [Cohere RAG](https://python.langchain.com/docs/integrations/retrievers/cohere/)
      * [Contextual AI Reranker](https://python.langchain.com/docs/integrations/retrievers/contextual/)
      * [Dappier](https://python.langchain.com/docs/integrations/retrievers/dappier/)
      * [DocArray](https://python.langchain.com/docs/integrations/retrievers/docarray_retriever/)
      * [Dria](https://python.langchain.com/docs/integrations/retrievers/dria_index/)
      * [ElasticSearch BM25](https://python.langchain.com/docs/integrations/retrievers/elastic_search_bm25/)
      * [Elasticsearch](https://python.langchain.com/docs/integrations/retrievers/elasticsearch_retriever/)
      * [Embedchain](https://python.langchain.com/docs/integrations/retrievers/embedchain/)
      * [FlashRank reranker](https://python.langchain.com/docs/integrations/retrievers/flashrank-reranker/)
      * [Fleet AI Context](https://python.langchain.com/docs/integrations/retrievers/fleet_context/)
      * [Galaxia](https://python.langchain.com/docs/integrations/retrievers/galaxia-retriever/)
      * [Google Drive](https://python.langchain.com/docs/integrations/retrievers/google_drive/)
      * [Google Vertex AI Search](https://python.langchain.com/docs/integrations/retrievers/google_vertex_ai_search/)
      * [Graph RAG](https://python.langchain.com/docs/integrations/retrievers/graph_rag/)
      * [IBM watsonx.ai](https://python.langchain.com/docs/integrations/retrievers/ibm_watsonx_ranker/)
      * [JaguarDB Vector Database](https://python.langchain.com/docs/integrations/retrievers/jaguar/)
      * [Kay.ai](https://python.langchain.com/docs/integrations/retrievers/kay/)
      * [Kinetica Vectorstore based Retriever](https://python.langchain.com/docs/integrations/retrievers/kinetica/)
      * [kNN](https://python.langchain.com/docs/integrations/retrievers/knn/)
      * [LinkupSearchRetriever](https://python.langchain.com/docs/integrations/retrievers/linkup_search/)
      * [LLMLingua Document Compressor](https://python.langchain.com/docs/integrations/retrievers/llmlingua/)
      * [LOTR (Merger Retriever)](https://python.langchain.com/docs/integrations/retrievers/merger_retriever/)
      * [Metal](https://python.langchain.com/docs/integrations/retrievers/metal/)
      * [NanoPQ (Product Quantization)](https://python.langchain.com/docs/integrations/retrievers/nanopq/)
      * [Nebius](https://python.langchain.com/docs/integrations/retrievers/nebius/)
      * [needle](https://python.langchain.com/docs/integrations/retrievers/needle/)
      * [Nimble](https://python.langchain.com/docs/integrations/retrievers/nimble/)
      * [Outline](https://python.langchain.com/docs/integrations/retrievers/outline/)
      * [Permit](https://python.langchain.com/docs/integrations/retrievers/permit/)
      * [Pinecone Hybrid Search](https://python.langchain.com/docs/integrations/retrievers/pinecone_hybrid_search/)
      * [Pinecone Rerank](https://python.langchain.com/docs/integrations/retrievers/pinecone_rerank/)
      * [PubMed](https://python.langchain.com/docs/integrations/retrievers/pubmed/)
      * [Qdrant Sparse Vector](https://python.langchain.com/docs/integrations/retrievers/qdrant-sparse/)
      * [RAGatouille](https://python.langchain.com/docs/integrations/retrievers/ragatouille/)
      * [RePhraseQuery](https://python.langchain.com/docs/integrations/retrievers/re_phrase/)
      * [Rememberizer](https://python.langchain.com/docs/integrations/retrievers/rememberizer/)
      * [SEC filing](https://python.langchain.com/docs/integrations/retrievers/sec_filings/)
      * [Self-querying retrievers](https://python.langchain.com/docs/integrations/retrievers/self_query/)
      * [SVM](https://python.langchain.com/docs/integrations/retrievers/svm/)
      * [TavilySearchAPI](https://python.langchain.com/docs/integrations/retrievers/tavily/)
      * [TF-IDF](https://python.langchain.com/docs/integrations/retrievers/tf_idf/)
      * [**NeuralDB**](https://python.langchain.com/docs/integrations/retrievers/thirdai_neuraldb/)
      * [ValyuContext](https://python.langchain.com/docs/integrations/retrievers/valyu/)
      * [Vectorize](https://python.langchain.com/docs/integrations/retrievers/vectorize/)
      * [Vespa](https://python.langchain.com/docs/integrations/retrievers/vespa/)
      * [Wikipedia](https://python.langchain.com/docs/integrations/retrievers/wikipedia/)
      * [You.com](https://python.langchain.com/docs/integrations/retrievers/you-retriever/)
      * [Zep Cloud](https://python.langchain.com/docs/integrations/retrievers/zep_cloud_memorystore/)
      * [Zep Open Source](https://python.langchain.com/docs/integrations/retrievers/zep_memorystore/)
      * [Zilliz Cloud Pipeline](https://python.langchain.com/docs/integrations/retrievers/zilliz_cloud_pipeline/)
      * [Zotero](https://python.langchain.com/docs/integrations/retrievers/zotero/)
    * [Tools/Toolkits](https://python.langchain.com/docs/integrations/tools/)
      * [Tools](https://python.langchain.com/docs/integrations/tools/)
      * [ADS4GPTs](https://python.langchain.com/docs/integrations/tools/ads4gpts/)
      * [AgentQL](https://python.langchain.com/docs/integrations/tools/agentql/)
      * [AINetwork Toolkit](https://python.langchain.com/docs/integrations/tools/ainetwork/)
      * [Alpha Vantage](https://python.langchain.com/docs/integrations/tools/alpha_vantage/)
      * [Amadeus Toolkit](https://python.langchain.com/docs/integrations/tools/amadeus/)
      * [Apify Actor](https://python.langchain.com/docs/integrations/tools/apify_actors/)
      * [ArXiv](https://python.langchain.com/docs/integrations/tools/arxiv/)
      * [AskNews](https://python.langchain.com/docs/integrations/tools/asknews/)
      * [AWS Lambda](https://python.langchain.com/docs/integrations/tools/awslambda/)
      * [Azure AI Services Toolkit](https://python.langchain.com/docs/integrations/tools/azure_ai_services/)
      * [Azure Cognitive Services Toolkit](https://python.langchain.com/docs/integrations/tools/azure_cognitive_services/)
      * [Azure Container Apps dynamic sessions](https://python.langchain.com/docs/integrations/tools/azure_dynamic_sessions/)
      * [Shell (bash)](https://python.langchain.com/docs/integrations/tools/bash/)
      * [Bearly Code Interpreter](https://python.langchain.com/docs/integrations/tools/bearly/)
      * [Bing Search](https://python.langchain.com/docs/integrations/tools/bing_search/)
      * [Brave Search](https://python.langchain.com/docs/integrations/tools/brave_search/)
      * [BrightDataWebScraperAPI](https://python.langchain.com/docs/integrations/tools/brightdata-webscraperapi/)
      * [BrightDataSERP](https://python.langchain.com/docs/integrations/tools/brightdata_serp/)
      * [BrightDataUnlocker](https://python.langchain.com/docs/integrations/tools/brightdata_unlocker/)
      * [Cassandra Database Toolkit](https://python.langchain.com/docs/integrations/tools/cassandra_database/)
      * [CDP](https://python.langchain.com/docs/integrations/tools/cdp_agentkit/)
      * [ChatGPT Plugins](https://python.langchain.com/docs/integrations/tools/chatgpt_plugins/)
      * [ClickUp Toolkit](https://python.langchain.com/docs/integrations/tools/clickup/)
      * [Cogniswitch Toolkit](https://python.langchain.com/docs/integrations/tools/cogniswitch/)
      * [Compass DeFi Toolkit](https://python.langchain.com/docs/integrations/tools/compass/)
      * [Connery Toolkit and Tools](https://python.langchain.com/docs/integrations/tools/connery/)
      * [Dall-E Image Generator](https://python.langchain.com/docs/integrations/tools/dalle_image_generator/)
      * [Dappier](https://python.langchain.com/docs/integrations/tools/dappier/)
      * [Databricks Unity Catalog (UC)](https://python.langchain.com/docs/integrations/tools/databricks/)
      * [DataForSEO](https://python.langchain.com/docs/integrations/tools/dataforseo/)
      * [Dataherald](https://python.langchain.com/docs/integrations/tools/dataherald/)
      * [DuckDuckGo Search](https://python.langchain.com/docs/integrations/tools/ddg/)
      * [Discord](https://python.langchain.com/docs/integrations/tools/discord/)
      * [E2B Data Analysis](https://python.langchain.com/docs/integrations/tools/e2b_data_analysis/)
      * [Eden AI](https://python.langchain.com/docs/integrations/tools/edenai_tools/)
      * [ElevenLabs Text2Speech](https://python.langchain.com/docs/integrations/tools/eleven_labs_tts/)
      * [Exa Search](https://python.langchain.com/docs/integrations/tools/exa_search/)
      * [File System](https://python.langchain.com/docs/integrations/tools/filesystem/)
      * [FinancialDatasets Toolkit](https://python.langchain.com/docs/integrations/tools/financial_datasets/)
      * [FMP Data](https://python.langchain.com/docs/integrations/tools/fmp-data/)
      * [Github Toolkit](https://python.langchain.com/docs/integrations/tools/github/)
      * [Gitlab Toolkit](https://python.langchain.com/docs/integrations/tools/gitlab/)
      * [Gmail Toolkit](https://python.langchain.com/docs/integrations/tools/gmail/)
      * [GOAT](https://python.langchain.com/docs/integrations/tools/goat/)
      * [Golden Query](https://python.langchain.com/docs/integrations/tools/golden_query/)
      * [Google Books](https://python.langchain.com/docs/integrations/tools/google_books/)
      * [Google Calendar Toolkit](https://python.langchain.com/docs/integrations/tools/google_calendar/)
      * [Google Cloud Text-to-Speech](https://python.langchain.com/docs/integrations/tools/google_cloud_texttospeech/)
      * [Google Drive](https://python.langchain.com/docs/integrations/tools/google_drive/)
      * [Google Finance](https://python.langchain.com/docs/integrations/tools/google_finance/)
      * [Google Imagen](https://python.langchain.com/docs/integrations/tools/google_imagen/)
      * [Google Jobs](https://python.langchain.com/docs/integrations/tools/google_jobs/)
      * [Google Lens](https://python.langchain.com/docs/integrations/tools/google_lens/)
      * [Google Places](https://python.langchain.com/docs/integrations/tools/google_places/)
      * [Google Scholar](https://python.langchain.com/docs/integrations/tools/google_scholar/)
      * [Google Search](https://python.langchain.com/docs/integrations/tools/google_search/)
      * [Google Serper](https://python.langchain.com/docs/integrations/tools/google_serper/)
      * [Google Trends](https://python.langchain.com/docs/integrations/tools/google_trends/)
      * [Gradio](https://python.langchain.com/docs/integrations/tools/gradio_tools/)
      * [GraphQL](https://python.langchain.com/docs/integrations/tools/graphql/)
      * [HuggingFace Hub Tools](https://python.langchain.com/docs/integrations/tools/huggingface_tools/)
      * [Human as a tool](https://python.langchain.com/docs/integrations/tools/human_tools/)
      * [Hyperbrowser Browser Agent Tools](https://python.langchain.com/docs/integrations/tools/hyperbrowser_browser_agent_tools/)
      * [Hyperbrowser Web Scraping Tools](https://python.langchain.com/docs/integrations/tools/hyperbrowser_web_scraping_tools/)
      * [IBM watsonx.ai](https://python.langchain.com/docs/integrations/tools/ibm_watsonx/)
      * [IFTTT WebHooks](https://python.langchain.com/docs/integrations/tools/ifttt/)
      * [Infobip](https://python.langchain.com/docs/integrations/tools/infobip/)
      * [Ionic Shopping Tool](https://python.langchain.com/docs/integrations/tools/ionic_shopping/)
      * [Jenkins](https://python.langchain.com/docs/integrations/tools/jenkins/)
      * [Jina Search](https://python.langchain.com/docs/integrations/tools/jina_search/)
      * [Jira Toolkit](https://python.langchain.com/docs/integrations/tools/jira/)
      * [JSON Toolkit](https://python.langchain.com/docs/integrations/tools/json/)
      * [Lemon Agent](https://python.langchain.com/docs/integrations/tools/lemonai/)
      * [LinkupSearchTool](https://python.langchain.com/docs/integrations/tools/linkup_search/)
      * [Memgraph](https://python.langchain.com/docs/integrations/tools/memgraph/)
      * [Memorize](https://python.langchain.com/docs/integrations/tools/memorize/)
      * [Mojeek Search](https://python.langchain.com/docs/integrations/tools/mojeek_search/)
      * [MultiOn Toolkit](https://python.langchain.com/docs/integrations/tools/multion/)
      * [NASA Toolkit](https://python.langchain.com/docs/integrations/tools/nasa/)
      * [Naver Search](https://python.langchain.com/docs/integrations/tools/naver_search/)
      * [Nuclia Understanding](https://python.langchain.com/docs/integrations/tools/nuclia/)
      * [NVIDIA Riva: ASR and TTS](https://python.langchain.com/docs/integrations/tools/nvidia_riva/)
      * [Office365 Toolkit](https://python.langchain.com/docs/integrations/tools/office365/)
      * [OpenAPI Toolkit](https://python.langchain.com/docs/integrations/tools/openapi/)
      * [Natural Language API Toolkits](https://python.langchain.com/docs/integrations/tools/openapi_nla/)
      * [OpenGradient](https://python.langchain.com/docs/integrations/tools/opengradient_toolkit/)
      * [OpenWeatherMap](https://python.langchain.com/docs/integrations/tools/openweathermap/)
      * [Oracle AI Vector Search: Generate Summary](https://python.langchain.com/docs/integrations/tools/oracleai/)
      * [Oxylabs](https://python.langchain.com/docs/integrations/tools/oxylabs/)
      * [Pandas Dataframe](https://python.langchain.com/docs/integrations/tools/pandas/)
      * [Passio NutritionAI](https://python.langchain.com/docs/integrations/tools/passio_nutrition_ai/)
      * [PaymanAI](https://python.langchain.com/docs/integrations/tools/payman-tool/)
      * [Permit](https://python.langchain.com/docs/integrations/tools/permit/)
      * [PlayWright Browser Toolkit](https://python.langchain.com/docs/integrations/tools/playwright/)
      * [Polygon IO Toolkit and Tools](https://python.langchain.com/docs/integrations/tools/polygon/)
      * [PowerBI Toolkit](https://python.langchain.com/docs/integrations/tools/powerbi/)
      * [Prolog](https://python.langchain.com/docs/integrations/tools/prolog_tool/)
      * [PubMed](https://python.langchain.com/docs/integrations/tools/pubmed/)
      * [Python REPL](https://python.langchain.com/docs/integrations/tools/python/)
      * [Reddit Search](https://python.langchain.com/docs/integrations/tools/reddit_search/)
      * [Requests Toolkit](https://python.langchain.com/docs/integrations/tools/requests/)
      * [Riza Code Interpreter](https://python.langchain.com/docs/integrations/tools/riza/)
      * [Robocorp Toolkit](https://python.langchain.com/docs/integrations/tools/robocorp/)
      * [Salesforce](https://python.langchain.com/docs/integrations/tools/salesforce/)
      * [SceneXplain](https://python.langchain.com/docs/integrations/tools/sceneXplain/)
      * [ScrapeGraph](https://python.langchain.com/docs/integrations/tools/scrapegraph/)
      * [SearchApi](https://python.langchain.com/docs/integrations/tools/searchapi/)
      * [SearxNG Search](https://python.langchain.com/docs/integrations/tools/searx_search/)
      * [Semantic Scholar API Tool](https://python.langchain.com/docs/integrations/tools/semanticscholar/)
      * [SerpAPI](https://python.langchain.com/docs/integrations/tools/serpapi/)
      * [Slack Toolkit](https://python.langchain.com/docs/integrations/tools/slack/)
      * [Spark SQL Toolkit](https://python.langchain.com/docs/integrations/tools/spark_sql/)
      * [SQLDatabase Toolkit](https://python.langchain.com/docs/integrations/tools/sql_database/)
      * [StackExchange](https://python.langchain.com/docs/integrations/tools/stackexchange/)
      * [Steam Toolkit](https://python.langchain.com/docs/integrations/tools/steam/)
      * [Stripe](https://python.langchain.com/docs/integrations/tools/stripe/)
      * [Tableau](https://python.langchain.com/docs/integrations/tools/tableau/)
      * [Taiga](https://python.langchain.com/docs/integrations/tools/taiga/)
      * [Tavily Extract](https://python.langchain.com/docs/integrations/tools/tavily_extract/)
      * [Tavily Search](https://python.langchain.com/docs/integrations/tools/tavily_search/)
      * [Tilores](https://python.langchain.com/docs/integrations/tools/tilores/)
      * [Twilio](https://python.langchain.com/docs/integrations/tools/twilio/)
      * [Upstage](https://python.langchain.com/docs/integrations/tools/upstage_groundedness_check/)
      * [Valthera](https://python.langchain.com/docs/integrations/tools/valthera/)
      * [ValyuContext](https://python.langchain.com/docs/integrations/tools/valyu_search/)
      * [Vectara](https://python.langchain.com/docs/integrations/tools/vectara/)
      * [Wikidata](https://python.langchain.com/docs/integrations/tools/wikidata/)
      * [Wikipedia](https://python.langchain.com/docs/integrations/tools/wikipedia/)
      * [Wolfram Alpha](https://python.langchain.com/docs/integrations/tools/wolfram_alpha/)
      * [Writer Tools](https://python.langchain.com/docs/integrations/tools/writer/)
      * [Yahoo Finance News](https://python.langchain.com/docs/integrations/tools/yahoo_finance_news/)
      * [You.com Search](https://python.langchain.com/docs/integrations/tools/you/)
      * [YouTube](https://python.langchain.com/docs/integrations/tools/youtube/)
      * [Zapier Natural Language Actions](https://python.langchain.com/docs/integrations/tools/zapier/)
      * [ZenGuard AI](https://python.langchain.com/docs/integrations/tools/zenguard/)
    * [Document loaders](https://python.langchain.com/docs/integrations/document_loaders/)
      * [Document loaders](https://python.langchain.com/docs/integrations/document_loaders/)
      * [acreom](https://python.langchain.com/docs/integrations/document_loaders/acreom/)
      * [AgentQLLoader](https://python.langchain.com/docs/integrations/document_loaders/agentql/)
      * [AirbyteLoader](https://python.langchain.com/docs/integrations/document_loaders/airbyte/)
      * [Airbyte CDK (Deprecated)](https://python.langchain.com/docs/integrations/document_loaders/airbyte_cdk/)
      * [Airbyte Gong (Deprecated)](https://python.langchain.com/docs/integrations/document_loaders/airbyte_gong/)
      * [Airbyte Hubspot (Deprecated)](https://python.langchain.com/docs/integrations/document_loaders/airbyte_hubspot/)
      * [Airbyte JSON (Deprecated)](https://python.langchain.com/docs/integrations/document_loaders/airbyte_json/)
      * [Airbyte Salesforce (Deprecated)](https://python.langchain.com/docs/integrations/document_loaders/airbyte_salesforce/)
      * [Airbyte Shopify (Deprecated)](https://python.langchain.com/docs/integrations/document_loaders/airbyte_shopify/)
      * [Airbyte Stripe (Deprecated)](https://python.langchain.com/docs/integrations/document_loaders/airbyte_stripe/)
      * [Airbyte Typeform (Deprecated)](https://python.langchain.com/docs/integrations/document_loaders/airbyte_typeform/)
      * [Airbyte Zendesk Support (Deprecated)](https://python.langchain.com/docs/integrations/document_loaders/airbyte_zendesk_support/)
      * [Airtable](https://python.langchain.com/docs/integrations/document_loaders/airtable/)
      * [Alibaba Cloud MaxCompute](https://python.langchain.com/docs/integrations/document_loaders/alibaba_cloud_maxcompute/)
      * [Amazon Textract](https://python.langchain.com/docs/integrations/document_loaders/amazon_textract/)
      * [Apify Dataset](https://python.langchain.com/docs/integrations/document_loaders/apify_dataset/)
      * [ArcGIS](https://python.langchain.com/docs/integrations/document_loaders/arcgis/)
      * [ArxivLoader](https://python.langchain.com/docs/integrations/document_loaders/arxiv/)
      * [AssemblyAI Audio Transcripts](https://python.langchain.com/docs/integrations/document_loaders/assemblyai/)
      * [AstraDB](https://python.langchain.com/docs/integrations/document_loaders/astradb/)
      * [Async Chromium](https://python.langchain.com/docs/integrations/document_loaders/async_chromium/)
      * [AsyncHtml](https://python.langchain.com/docs/integrations/document_loaders/async_html/)
      * [Athena](https://python.langchain.com/docs/integrations/document_loaders/athena/)
      * [AWS S3 Directory](https://python.langchain.com/docs/integrations/document_loaders/aws_s3_directory/)
      * [AWS S3 File](https://python.langchain.com/docs/integrations/document_loaders/aws_s3_file/)
      * [AZLyrics](https://python.langchain.com/docs/integrations/document_loaders/azlyrics/)
      * [Azure AI Data](https://python.langchain.com/docs/integrations/document_loaders/azure_ai_data/)
      * [Azure Blob Storage Container](https://python.langchain.com/docs/integrations/document_loaders/azure_blob_storage_container/)
      * [Azure Blob Storage File](https://python.langchain.com/docs/integrations/document_loaders/azure_blob_storage_file/)
      * [Azure AI Document Intelligence](https://python.langchain.com/docs/integrations/document_loaders/azure_document_intelligence/)
      * [BibTeX](https://python.langchain.com/docs/integrations/document_loaders/bibtex/)
      * [BiliBili](https://python.langchain.com/docs/integrations/document_loaders/bilibili/)
      * [Blackboard](https://python.langchain.com/docs/integrations/document_loaders/blackboard/)
      * [Blockchain](https://python.langchain.com/docs/integrations/document_loaders/blockchain/)
      * [Box](https://python.langchain.com/docs/integrations/document_loaders/box/)
      * [Brave Search](https://python.langchain.com/docs/integrations/document_loaders/brave_search/)
      * [Browserbase](https://python.langchain.com/docs/integrations/document_loaders/browserbase/)
      * [Browserless](https://python.langchain.com/docs/integrations/document_loaders/browserless/)
      * [BSHTMLLoader](https://python.langchain.com/docs/integrations/document_loaders/bshtml/)
      * [Cassandra](https://python.langchain.com/docs/integrations/document_loaders/cassandra/)
      * [ChatGPT Data](https://python.langchain.com/docs/integrations/document_loaders/chatgpt_loader/)
      * [College Confidential](https://python.langchain.com/docs/integrations/document_loaders/college_confidential/)
      * [Concurrent Loader](https://python.langchain.com/docs/integrations/document_loaders/concurrent/)
      * [Confluence](https://python.langchain.com/docs/integrations/document_loaders/confluence/)
      * [CoNLL-U](https://python.langchain.com/docs/integrations/document_loaders/conll-u/)
      * [Copy Paste](https://python.langchain.com/docs/integrations/document_loaders/copypaste/)
      * [Couchbase](https://python.langchain.com/docs/integrations/document_loaders/couchbase/)
      * [CSV](https://python.langchain.com/docs/integrations/document_loaders/csv/)
      * [Cube Semantic Layer](https://python.langchain.com/docs/integrations/document_loaders/cube_semantic/)
      * [Datadog Logs](https://python.langchain.com/docs/integrations/document_loaders/datadog_logs/)
      * [Dedoc](https://python.langchain.com/docs/integrations/document_loaders/dedoc/)
      * [Diffbot](https://python.langchain.com/docs/integrations/document_loaders/diffbot/)
      * [Discord](https://python.langchain.com/docs/integrations/document_loaders/discord/)
      * [Docling](https://python.langchain.com/docs/integrations/document_loaders/docling/)
      * [Docugami](https://python.langchain.com/docs/integrations/document_loaders/docugami/)
      * [Docusaurus](https://python.langchain.com/docs/integrations/document_loaders/docusaurus/)
      * [Dropbox](https://python.langchain.com/docs/integrations/document_loaders/dropbox/)
      * [DuckDB](https://python.langchain.com/docs/integrations/document_loaders/duckdb/)
      * [Email](https://python.langchain.com/docs/integrations/document_loaders/email/)
      * [EPub](https://python.langchain.com/docs/integrations/document_loaders/epub/)
      * [Etherscan](https://python.langchain.com/docs/integrations/document_loaders/etherscan/)
      * [EverNote](https://python.langchain.com/docs/integrations/document_loaders/evernote/)
      * [example_data](https://python.langchain.com/docs/integrations/document_loaders/example_data/example/)
      * [Facebook Chat](https://python.langchain.com/docs/integrations/document_loaders/facebook_chat/)
      * [Fauna](https://python.langchain.com/docs/integrations/document_loaders/fauna/)
      * [Figma](https://python.langchain.com/docs/integrations/document_loaders/figma/)
      * [FireCrawl](https://python.langchain.com/docs/integrations/document_loaders/firecrawl/)
      * [Geopandas](https://python.langchain.com/docs/integrations/document_loaders/geopandas/)
      * [Git](https://python.langchain.com/docs/integrations/document_loaders/git/)
      * [GitBook](https://python.langchain.com/docs/integrations/document_loaders/gitbook/)
      * [GitHub](https://python.langchain.com/docs/integrations/document_loaders/github/)
      * [Glue Catalog](https://python.langchain.com/docs/integrations/document_loaders/glue_catalog/)
      * [Google AlloyDB for PostgreSQL](https://python.langchain.com/docs/integrations/document_loaders/google_alloydb/)
      * [Google BigQuery](https://python.langchain.com/docs/integrations/document_loaders/google_bigquery/)
      * [Google Bigtable](https://python.langchain.com/docs/integrations/document_loaders/google_bigtable/)
      * [Google Cloud SQL for SQL server](https://python.langchain.com/docs/integrations/document_loaders/google_cloud_sql_mssql/)
      * [Google Cloud SQL for MySQL](https://python.langchain.com/docs/integrations/document_loaders/google_cloud_sql_mysql/)
      * [Google Cloud SQL for PostgreSQL](https://python.langchain.com/docs/integrations/document_loaders/google_cloud_sql_pg/)
      * [Google Cloud Storage Directory](https://python.langchain.com/docs/integrations/document_loaders/google_cloud_storage_directory/)
      * [Google Cloud Storage File](https://python.langchain.com/docs/integrations/document_loaders/google_cloud_storage_file/)
      * [Google Firestore in Datastore Mode](https://python.langchain.com/docs/integrations/document_loaders/google_datastore/)
      * [Google Drive](https://python.langchain.com/docs/integrations/document_loaders/google_drive/)
      * [Google El Carro for Oracle Workloads](https://python.langchain.com/docs/integrations/document_loaders/google_el_carro/)
      * [Google Firestore (Native Mode)](https://python.langchain.com/docs/integrations/document_loaders/google_firestore/)
      * [Google Memorystore for Redis](https://python.langchain.com/docs/integrations/document_loaders/google_memorystore_redis/)
      * [Google Spanner](https://python.langchain.com/docs/integrations/document_loaders/google_spanner/)
      * [Google Speech-to-Text Audio Transcripts](https://python.langchain.com/docs/integrations/document_loaders/google_speech_to_text/)
      * [Grobid](https://python.langchain.com/docs/integrations/document_loaders/grobid/)
      * [Gutenberg](https://python.langchain.com/docs/integrations/document_loaders/gutenberg/)
      * [Hacker News](https://python.langchain.com/docs/integrations/document_loaders/hacker_news/)
      * [Huawei OBS Directory](https://python.langchain.com/docs/integrations/document_loaders/huawei_obs_directory/)
      * [Huawei OBS File](https://python.langchain.com/docs/integrations/document_loaders/huawei_obs_file/)
      * [HuggingFace dataset](https://python.langchain.com/docs/integrations/document_loaders/hugging_face_dataset/)
      * [HyperbrowserLoader](https://python.langchain.com/docs/integrations/document_loaders/hyperbrowser/)
      * [iFixit](https://python.langchain.com/docs/integrations/document_loaders/ifixit/)
      * [Images](https://python.langchain.com/docs/integrations/document_loaders/image/)
      * [Image captions](https://python.langchain.com/docs/integrations/document_loaders/image_captions/)
      * [IMSDb](https://python.langchain.com/docs/integrations/document_loaders/imsdb/)
      * [Iugu](https://python.langchain.com/docs/integrations/document_loaders/iugu/)
      * [Joplin](https://python.langchain.com/docs/integrations/document_loaders/joplin/)
      * [JSONLoader](https://python.langchain.com/docs/integrations/document_loaders/json/)
      * [Jupyter Notebook](https://python.langchain.com/docs/integrations/document_loaders/jupyter_notebook/)
      * [Kinetica](https://python.langchain.com/docs/integrations/document_loaders/kinetica/)
      * [lakeFS](https://python.langchain.com/docs/integrations/document_loaders/lakefs/)
      * [LangSmith](https://python.langchain.com/docs/integrations/document_loaders/langsmith/)
      * [LarkSuite (FeiShu)](https://python.langchain.com/docs/integrations/document_loaders/larksuite/)
      * [LLM Sherpa](https://python.langchain.com/docs/integrations/document_loaders/llmsherpa/)
      * [Mastodon](https://python.langchain.com/docs/integrations/document_loaders/mastodon/)
      * [MathPixPDFLoader](https://python.langchain.com/docs/integrations/document_loaders/mathpix/)
      * [MediaWiki Dump](https://python.langchain.com/docs/integrations/document_loaders/mediawikidump/)
      * [Merge Documents Loader](https://python.langchain.com/docs/integrations/document_loaders/merge_doc/)
      * [mhtml](https://python.langchain.com/docs/integrations/document_loaders/mhtml/)
      * [Microsoft Excel](https://python.langchain.com/docs/integrations/document_loaders/microsoft_excel/)
      * [Microsoft OneDrive](https://python.langchain.com/docs/integrations/document_loaders/microsoft_onedrive/)
      * [Microsoft OneNote](https://python.langchain.com/docs/integrations/document_loaders/microsoft_onenote/)
      * [Microsoft PowerPoint](https://python.langchain.com/docs/integrations/document_loaders/microsoft_powerpoint/)
      * [Microsoft SharePoint](https://python.langchain.com/docs/integrations/document_loaders/microsoft_sharepoint/)
      * [Microsoft Word](https://python.langchain.com/docs/integrations/document_loaders/microsoft_word/)
      * [Near Blockchain](https://python.langchain.com/docs/integrations/document_loaders/mintbase/)
      * [Modern Treasury](https://python.langchain.com/docs/integrations/document_loaders/modern_treasury/)
      * [MongoDB](https://python.langchain.com/docs/integrations/document_loaders/mongodb/)
      * [Needle Document Loader](https://python.langchain.com/docs/integrations/document_loaders/needle/)
      * [News URL](https://python.langchain.com/docs/integrations/document_loaders/news/)
      * [Notion DB 2/2](https://python.langchain.com/docs/integrations/document_loaders/notion/)
      * [Nuclia](https://python.langchain.com/docs/integrations/document_loaders/nuclia/)
      * [Obsidian](https://python.langchain.com/docs/integrations/document_loaders/obsidian/)
      * [Open Document Format (ODT)](https://python.langchain.com/docs/integrations/document_loaders/odt/)
      * [Open City Data](https://python.langchain.com/docs/integrations/document_loaders/open_city_data/)
      * [Oracle Autonomous Database](https://python.langchain.com/docs/integrations/document_loaders/oracleadb_loader/)
      * [Oracle AI Vector Search: Document Processing](https://python.langchain.com/docs/integrations/document_loaders/oracleai/)
      * [Org-mode](https://python.langchain.com/docs/integrations/document_loaders/org_mode/)
      * [Outline Document Loader](https://python.langchain.com/docs/integrations/document_loaders/outline/)
      * [Pandas DataFrame](https://python.langchain.com/docs/integrations/document_loaders/pandas_dataframe/)
      * [parsers](https://python.langchain.com/docs/integrations/document_loaders/parsers/azure_openai_whisper_parser/)
      * [PDFMinerLoader](https://python.langchain.com/docs/integrations/document_loaders/pdfminer/)
      * [PDFPlumber](https://python.langchain.com/docs/integrations/document_loaders/pdfplumber/)
      * [Pebblo Safe DocumentLoader](https://python.langchain.com/docs/integrations/document_loaders/pebblo/)
      * [Polars DataFrame](https://python.langchain.com/docs/integrations/document_loaders/polars_dataframe/)
      * [Dell PowerScale Document Loader](https://python.langchain.com/docs/integrations/document_loaders/powerscale/)
      * [Psychic](https://python.langchain.com/docs/integrations/document_loaders/psychic/)
      * [PubMed](https://python.langchain.com/docs/integrations/document_loaders/pubmed/)
      * [PullMdLoader](https://python.langchain.com/docs/integrations/document_loaders/pull_md/)
      * [PyMuPDFLoader](https://python.langchain.com/docs/integrations/document_loaders/pymupdf/)
      * [PyMuPDF4LLM](https://python.langchain.com/docs/integrations/document_loaders/pymupdf4llm/)
      * [PyPDFDirectoryLoader](https://python.langchain.com/docs/integrations/document_loaders/pypdfdirectory/)
      * [PyPDFium2Loader](https://python.langchain.com/docs/integrations/document_loaders/pypdfium2/)
      * [PyPDFLoader](https://python.langchain.com/docs/integrations/document_loaders/pypdfloader/)
      * [PySpark](https://python.langchain.com/docs/integrations/document_loaders/pyspark_dataframe/)
      * [Quip](https://python.langchain.com/docs/integrations/document_loaders/quip/)
      * [ReadTheDocs Documentation](https://python.langchain.com/docs/integrations/document_loaders/readthedocs_documentation/)
      * [Recursive URL](https://python.langchain.com/docs/integrations/document_loaders/recursive_url/)
      * [Reddit](https://python.langchain.com/docs/integrations/document_loaders/reddit/)
      * [Roam](https://python.langchain.com/docs/integrations/document_loaders/roam/)
      * [Rockset](https://python.langchain.com/docs/integrations/document_loaders/rockset/)
      * [rspace](https://python.langchain.com/docs/integrations/document_loaders/rspace/)
      * [RSS Feeds](https://python.langchain.com/docs/integrations/document_loaders/rss/)
      * [RST](https://python.langchain.com/docs/integrations/document_loaders/rst/)
      * [scrapfly](https://python.langchain.com/docs/integrations/document_loaders/scrapfly/)
      * [ScrapingAnt](https://python.langchain.com/docs/integrations/document_loaders/scrapingant/)
      * [SingleStore](https://python.langchain.com/docs/integrations/document_loaders/singlestore/)
      * [Sitemap](https://python.langchain.com/docs/integrations/document_loaders/sitemap/)
      * [Slack](https://python.langchain.com/docs/integrations/document_loaders/slack/)
      * [Snowflake](https://python.langchain.com/docs/integrations/document_loaders/snowflake/)
      * [Source Code](https://python.langchain.com/docs/integrations/document_loaders/source_code/)
      * [Spider](https://python.langchain.com/docs/integrations/document_loaders/spider/)
      * [Spreedly](https://python.langchain.com/docs/integrations/document_loaders/spreedly/)
      * [Stripe](https://python.langchain.com/docs/integrations/document_loaders/stripe/)
      * [Subtitle](https://python.langchain.com/docs/integrations/document_loaders/subtitle/)
      * [SurrealDB](https://python.langchain.com/docs/integrations/document_loaders/surrealdb/)
      * [Telegram](https://python.langchain.com/docs/integrations/document_loaders/telegram/)
      * [Tencent COS Directory](https://python.langchain.com/docs/integrations/document_loaders/tencent_cos_directory/)
      * [Tencent COS File](https://python.langchain.com/docs/integrations/document_loaders/tencent_cos_file/)
      * [TensorFlow Datasets](https://python.langchain.com/docs/integrations/document_loaders/tensorflow_datasets/)
      * [TiDB](https://python.langchain.com/docs/integrations/document_loaders/tidb/)
      * [2Markdown](https://python.langchain.com/docs/integrations/document_loaders/tomarkdown/)
      * [TOML](https://python.langchain.com/docs/integrations/document_loaders/toml/)
      * [Trello](https://python.langchain.com/docs/integrations/document_loaders/trello/)
      * [TSV](https://python.langchain.com/docs/integrations/document_loaders/tsv/)
      * [Twitter](https://python.langchain.com/docs/integrations/document_loaders/twitter/)
      * [Unstructured](https://python.langchain.com/docs/integrations/document_loaders/unstructured_file/)
      * [UnstructuredMarkdownLoader](https://python.langchain.com/docs/integrations/document_loaders/unstructured_markdown/)
      * [UnstructuredPDFLoader](https://python.langchain.com/docs/integrations/document_loaders/unstructured_pdfloader/)
      * [Upstage](https://python.langchain.com/docs/integrations/document_loaders/upstage/)
      * [URL](https://python.langchain.com/docs/integrations/document_loaders/url/)
      * [Vsdx](https://python.langchain.com/docs/integrations/document_loaders/vsdx/)
      * [Weather](https://python.langchain.com/docs/integrations/document_loaders/weather/)
      * [WebBaseLoader](https://python.langchain.com/docs/integrations/document_loaders/web_base/)
      * [WhatsApp Chat](https://python.langchain.com/docs/integrations/document_loaders/whatsapp_chat/)
      * [Wikipedia](https://python.langchain.com/docs/integrations/document_loaders/wikipedia/)
      * [UnstructuredXMLLoader](https://python.langchain.com/docs/integrations/document_loaders/xml/)
      * [Xorbits Pandas DataFrame](https://python.langchain.com/docs/integrations/document_loaders/xorbits/)
      * [YouTube audio](https://python.langchain.com/docs/integrations/document_loaders/youtube_audio/)
      * [YouTube transcripts](https://python.langchain.com/docs/integrations/document_loaders/youtube_transcript/)
      * [YoutubeLoaderDL](https://python.langchain.com/docs/integrations/document_loaders/yt_dlp/)
      * [Yuque](https://python.langchain.com/docs/integrations/document_loaders/yuque/)
      * [ZeroxPDFLoader](https://python.langchain.com/docs/integrations/document_loaders/zeroxpdfloader/)
    * [Vector stores](https://python.langchain.com/docs/integrations/vectorstores/)
      * [Vector stores](https://python.langchain.com/docs/integrations/vectorstores/)
      * [Activeloop Deep Lake](https://python.langchain.com/docs/integrations/vectorstores/activeloop_deeplake/)
      * [Aerospike](https://python.langchain.com/docs/integrations/vectorstores/aerospike/)
      * [Alibaba Cloud OpenSearch](https://python.langchain.com/docs/integrations/vectorstores/alibabacloud_opensearch/)
      * [AnalyticDB](https://python.langchain.com/docs/integrations/vectorstores/analyticdb/)
      * [Annoy](https://python.langchain.com/docs/integrations/vectorstores/annoy/)
      * [Apache Doris](https://python.langchain.com/docs/integrations/vectorstores/apache_doris/)
      * [ApertureDB](https://python.langchain.com/docs/integrations/vectorstores/aperturedb/)
      * [Astra DB Vector Store](https://python.langchain.com/docs/integrations/vectorstores/astradb/)
      * [Atlas](https://python.langchain.com/docs/integrations/vectorstores/atlas/)
      * [AwaDB](https://python.langchain.com/docs/integrations/vectorstores/awadb/)
      * [Azure Cosmos DB Mongo vCore](https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db/)
      * [Azure Cosmos DB No SQL](https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db_no_sql/)
      * [Azure AI Search](https://python.langchain.com/docs/integrations/vectorstores/azuresearch/)
      * [Bagel](https://python.langchain.com/docs/integrations/vectorstores/bagel/)
      * [BagelDB](https://python.langchain.com/docs/integrations/vectorstores/bageldb/)
      * [Baidu Cloud ElasticSearch VectorSearch](https://python.langchain.com/docs/integrations/vectorstores/baiducloud_vector_search/)
      * [Baidu VectorDB](https://python.langchain.com/docs/integrations/vectorstores/baiduvectordb/)
      * [Apache Cassandra](https://python.langchain.com/docs/integrations/vectorstores/cassandra/)
      * [Chroma](https://python.langchain.com/docs/integrations/vectorstores/chroma/)
      * [Clarifai](https://python.langchain.com/docs/integrations/vectorstores/clarifai/)
      * [ClickHouse](https://python.langchain.com/docs/integrations/vectorstores/clickhouse/)
      * [CloudflareVectorize](https://python.langchain.com/docs/integrations/vectorstores/cloudflare_vectorize/)
      * [Couchbase](https://python.langchain.com/docs/integrations/vectorstores/couchbase/)
      * [DashVector](https://python.langchain.com/docs/integrations/vectorstores/dashvector/)
      * [Databricks](https://python.langchain.com/docs/integrations/vectorstores/databricks_vector_search/)
      * [DingoDB](https://python.langchain.com/docs/integrations/vectorstores/dingo/)
      * [DocArray HnswSearch](https://python.langchain.com/docs/integrations/vectorstores/docarray_hnsw/)
      * [DocArray InMemorySearch](https://python.langchain.com/docs/integrations/vectorstores/docarray_in_memory/)
      * [Amazon Document DB](https://python.langchain.com/docs/integrations/vectorstores/documentdb/)
      * [DuckDB](https://python.langchain.com/docs/integrations/vectorstores/duckdb/)
      * [China Mobile ECloud ElasticSearch VectorSearch](https://python.langchain.com/docs/integrations/vectorstores/ecloud_vector_search/)
      * [Elasticsearch](https://python.langchain.com/docs/integrations/vectorstores/elasticsearch/)
      * [Epsilla](https://python.langchain.com/docs/integrations/vectorstores/epsilla/)
      * [Faiss](https://python.langchain.com/docs/integrations/vectorstores/faiss/)
      * [Faiss (Async)](https://python.langchain.com/docs/integrations/vectorstores/faiss_async/)
      * [FalkorDBVectorStore](https://python.langchain.com/docs/integrations/vectorstores/falkordbvector/)
      * [Gel](https://python.langchain.com/docs/integrations/vectorstores/gel/)
      * [Google AlloyDB for PostgreSQL](https://python.langchain.com/docs/integrations/vectorstores/google_alloydb/)
      * [Google BigQuery Vector Search](https://python.langchain.com/docs/integrations/vectorstores/google_bigquery_vector_search/)
      * [Google Cloud SQL for MySQL](https://python.langchain.com/docs/integrations/vectorstores/google_cloud_sql_mysql/)
      * [Google Cloud SQL for PostgreSQL](https://python.langchain.com/docs/integrations/vectorstores/google_cloud_sql_pg/)
      * [Firestore](https://python.langchain.com/docs/integrations/vectorstores/google_firestore/)
      * [Google Memorystore for Redis](https://python.langchain.com/docs/integrations/vectorstores/google_memorystore_redis/)
      * [Google Spanner](https://python.langchain.com/docs/integrations/vectorstores/google_spanner/)
      * [Google Vertex AI Feature Store](https://python.langchain.com/docs/integrations/vectorstores/google_vertex_ai_feature_store/)
      * [Google Vertex AI Vector Search](https://python.langchain.com/docs/integrations/vectorstores/google_vertex_ai_vector_search/)
      * [Hippo](https://python.langchain.com/docs/integrations/vectorstores/hippo/)
      * [Hologres](https://python.langchain.com/docs/integrations/vectorstores/hologres/)
      * [Infinispan](https://python.langchain.com/docs/integrations/vectorstores/infinispanvs/)
      * [Jaguar Vector Database](https://python.langchain.com/docs/integrations/vectorstores/jaguar/)
      * [KDB.AI](https://python.langchain.com/docs/integrations/vectorstores/kdbai/)
      * [Kinetica](https://python.langchain.com/docs/integrations/vectorstores/kinetica/)
      * [LanceDB](https://python.langchain.com/docs/integrations/vectorstores/lancedb/)
      * [Lantern](https://python.langchain.com/docs/integrations/vectorstores/lantern/)
      * [Lindorm](https://python.langchain.com/docs/integrations/vectorstores/lindorm/)
      * [LLMRails](https://python.langchain.com/docs/integrations/vectorstores/llm_rails/)
      * [ManticoreSearch VectorStore](https://python.langchain.com/docs/integrations/vectorstores/manticore_search/)
      * [MariaDB](https://python.langchain.com/docs/integrations/vectorstores/mariadb/)
      * [Marqo](https://python.langchain.com/docs/integrations/vectorstores/marqo/)
      * [Meilisearch](https://python.langchain.com/docs/integrations/vectorstores/meilisearch/)
      * [Amazon MemoryDB](https://python.langchain.com/docs/integrations/vectorstores/memorydb/)
      * [Milvus](https://python.langchain.com/docs/integrations/vectorstores/milvus/)
      * [Momento Vector Index (MVI)](https://python.langchain.com/docs/integrations/vectorstores/momento_vector_index/)
      * [MongoDB Atlas](https://python.langchain.com/docs/integrations/vectorstores/mongodb_atlas/)
      * [MyScale](https://python.langchain.com/docs/integrations/vectorstores/myscale/)
      * [Neo4j Vector Index](https://python.langchain.com/docs/integrations/vectorstores/neo4jvector/)
      * [NucliaDB](https://python.langchain.com/docs/integrations/vectorstores/nucliadb/)
      * [Oceanbase](https://python.langchain.com/docs/integrations/vectorstores/oceanbase/)
      * [openGauss](https://python.langchain.com/docs/integrations/vectorstores/opengauss/)
      * [OpenSearch](https://python.langchain.com/docs/integrations/vectorstores/opensearch/)
      * [Oracle AI Vector Search: Vector Store](https://python.langchain.com/docs/integrations/vectorstores/oracle/)
      * [Pathway](https://python.langchain.com/docs/integrations/vectorstores/pathway/)
      * [Postgres Embedding](https://python.langchain.com/docs/integrations/vectorstores/pgembedding/)
      * [PGVecto.rs](https://python.langchain.com/docs/integrations/vectorstores/pgvecto_rs/)
      * [PGVector](https://python.langchain.com/docs/integrations/vectorstores/pgvector/)
      * [Pinecone](https://python.langchain.com/docs/integrations/vectorstores/pinecone/)
      * [Pinecone (sparse)](https://python.langchain.com/docs/integrations/vectorstores/pinecone_sparse/)
      * [Qdrant](https://python.langchain.com/docs/integrations/vectorstores/qdrant/)
      * [Redis](https://python.langchain.com/docs/integrations/vectorstores/redis/)
      * [Relyt](https://python.langchain.com/docs/integrations/vectorstores/relyt/)
      * [Rockset](https://python.langchain.com/docs/integrations/vectorstores/rockset/)
      * [SAP HANA Cloud Vector Engine](https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/)
      * [ScaNN](https://python.langchain.com/docs/integrations/vectorstores/scann/)
      * [SemaDB](https://python.langchain.com/docs/integrations/vectorstores/semadb/)
      * [SingleStore](https://python.langchain.com/docs/integrations/vectorstores/singlestore/)
      * [scikit-learn](https://python.langchain.com/docs/integrations/vectorstores/sklearn/)
      * [SQLiteVec](https://python.langchain.com/docs/integrations/vectorstores/sqlitevec/)
      * [SQLite-VSS](https://python.langchain.com/docs/integrations/vectorstores/sqlitevss/)
      * [SQLServer](https://python.langchain.com/docs/integrations/vectorstores/sqlserver/)
      * [StarRocks](https://python.langchain.com/docs/integrations/vectorstores/starrocks/)
      * [Supabase (Postgres)](https://python.langchain.com/docs/integrations/vectorstores/supabase/)
      * [SurrealDB](https://python.langchain.com/docs/integrations/vectorstores/surrealdb/)
      * [Tablestore](https://python.langchain.com/docs/integrations/vectorstores/tablestore/)
      * [Tair](https://python.langchain.com/docs/integrations/vectorstores/tair/)
      * [Tencent Cloud VectorDB](https://python.langchain.com/docs/integrations/vectorstores/tencentvectordb/)
      * [ThirdAI NeuralDB](https://python.langchain.com/docs/integrations/vectorstores/thirdai_neuraldb/)
      * [TiDB Vector](https://python.langchain.com/docs/integrations/vectorstores/tidb_vector/)
      * [Tigris](https://python.langchain.com/docs/integrations/vectorstores/tigris/)
      * [TileDB](https://python.langchain.com/docs/integrations/vectorstores/tiledb/)
      * [Timescale Vector (Postgres)](https://python.langchain.com/docs/integrations/vectorstores/timescalevector/)
      * [Typesense](https://python.langchain.com/docs/integrations/vectorstores/typesense/)
      * [Upstash Vector](https://python.langchain.com/docs/integrations/vectorstores/upstash/)
      * [USearch](https://python.langchain.com/docs/integrations/vectorstores/usearch/)
      * [Vald](https://python.langchain.com/docs/integrations/vectorstores/vald/)
      * [VDMS](https://python.langchain.com/docs/integrations/vectorstores/vdms/)
      * [Vearch](https://python.langchain.com/docs/integrations/vectorstores/vearch/)
      * [Vectara](https://python.langchain.com/docs/integrations/vectorstores/vectara/)
      * [Vespa](https://python.langchain.com/docs/integrations/vectorstores/vespa/)
      * [viking DB](https://python.langchain.com/docs/integrations/vectorstores/vikingdb/)
      * [vlite](https://python.langchain.com/docs/integrations/vectorstores/vlite/)
      * [Weaviate](https://python.langchain.com/docs/integrations/vectorstores/weaviate/)
      * [Xata](https://python.langchain.com/docs/integrations/vectorstores/xata/)
      * [YDB](https://python.langchain.com/docs/integrations/vectorstores/ydb/)
      * [Yellowbrick](https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/)
      * [Zep](https://python.langchain.com/docs/integrations/vectorstores/zep/)
      * [Zep Cloud](https://python.langchain.com/docs/integrations/vectorstores/zep_cloud/)
      * [Zilliz](https://python.langchain.com/docs/integrations/vectorstores/zilliz/)
    * [Embedding models](https://python.langchain.com/docs/integrations/text_embedding/)
      * [Embedding models](https://python.langchain.com/docs/integrations/text_embedding/)
      * [AI21](https://python.langchain.com/docs/integrations/text_embedding/ai21/)
      * [Aleph Alpha](https://python.langchain.com/docs/integrations/text_embedding/aleph_alpha/)
      * [Anyscale](https://python.langchain.com/docs/integrations/text_embedding/anyscale/)
      * [ascend](https://python.langchain.com/docs/integrations/text_embedding/ascend/)
      * [AwaDB](https://python.langchain.com/docs/integrations/text_embedding/awadb/)
      * [AzureOpenAI](https://python.langchain.com/docs/integrations/text_embedding/azureopenai/)
      * [Baichuan Text Embeddings](https://python.langchain.com/docs/integrations/text_embedding/baichuan/)
      * [Baidu Qianfan](https://python.langchain.com/docs/integrations/text_embedding/baidu_qianfan_endpoint/)
      * [Bedrock](https://python.langchain.com/docs/integrations/text_embedding/bedrock/)
      * [BGE on Hugging Face](https://python.langchain.com/docs/integrations/text_embedding/bge_huggingface/)
      * [Bookend AI](https://python.langchain.com/docs/integrations/text_embedding/bookend/)
      * [Clarifai](https://python.langchain.com/docs/integrations/text_embedding/clarifai/)
      * [Cloudflare Workers AI](https://python.langchain.com/docs/integrations/text_embedding/cloudflare_workersai/)
      * [Clova Embeddings](https://python.langchain.com/docs/integrations/text_embedding/clova/)
      * [Cohere](https://python.langchain.com/docs/integrations/text_embedding/cohere/)
      * [DashScope](https://python.langchain.com/docs/integrations/text_embedding/dashscope/)
      * [Databricks](https://python.langchain.com/docs/integrations/text_embedding/databricks/)
      * [DeepInfra](https://python.langchain.com/docs/integrations/text_embedding/deepinfra/)
      * [EDEN AI](https://python.langchain.com/docs/integrations/text_embedding/edenai/)
      * [Elasticsearch](https://python.langchain.com/docs/integrations/text_embedding/elasticsearch/)
      * [Embaas](https://python.langchain.com/docs/integrations/text_embedding/embaas/)
      * [ERNIE](https://python.langchain.com/docs/integrations/text_embedding/ernie/)
      * [Fake Embeddings](https://python.langchain.com/docs/integrations/text_embedding/fake/)
      * [FastEmbed by Qdrant](https://python.langchain.com/docs/integrations/text_embedding/fastembed/)
      * [Fireworks](https://python.langchain.com/docs/integrations/text_embedding/fireworks/)
      * [Google Gemini](https://python.langchain.com/docs/integrations/text_embedding/google_generative_ai/)
      * [Google Vertex AI](https://python.langchain.com/docs/integrations/text_embedding/google_vertex_ai_palm/)
      * [GPT4All](https://python.langchain.com/docs/integrations/text_embedding/gpt4all/)
      * [Gradient](https://python.langchain.com/docs/integrations/text_embedding/gradient/)
      * [Hugging Face](https://python.langchain.com/docs/integrations/text_embedding/huggingfacehub/)
      * [IBM watsonx.ai](https://python.langchain.com/docs/integrations/text_embedding/ibm_watsonx/)
      * [Infinity](https://python.langchain.com/docs/integrations/text_embedding/infinity/)
      * [Instruct Embeddings on Hugging Face](https://python.langchain.com/docs/integrations/text_embedding/instruct_embeddings/)
      * [IPEX-LLM: Local BGE Embeddings on Intel CPU](https://python.langchain.com/docs/integrations/text_embedding/ipex_llm/)
      * [IPEX-LLM: Local BGE Embeddings on Intel GPU](https://python.langchain.com/docs/integrations/text_embedding/ipex_llm_gpu/)
      * [Intel® Extension for Transformers Quantized Text Embeddings](https://python.langchain.com/docs/integrations/text_embedding/itrex/)
      * [Jina](https://python.langchain.com/docs/integrations/text_embedding/jina/)
      * [John Snow Labs](https://python.langchain.com/docs/integrations/text_embedding/johnsnowlabs_embedding/)
      * [LASER Language-Agnostic SEntence Representations Embeddings by Meta AI](https://python.langchain.com/docs/integrations/text_embedding/laser/)
      * [Lindorm](https://python.langchain.com/docs/integrations/text_embedding/lindorm/)
      * [Llama.cpp](https://python.langchain.com/docs/integrations/text_embedding/llamacpp/)
      * [llamafile](https://python.langchain.com/docs/integrations/text_embedding/llamafile/)
      * [LLMRails](https://python.langchain.com/docs/integrations/text_embedding/llm_rails/)
      * [LocalAI](https://python.langchain.com/docs/integrations/text_embedding/localai/)
      * [MiniMax](https://python.langchain.com/docs/integrations/text_embedding/minimax/)
      * [MistralAI](https://python.langchain.com/docs/integrations/text_embedding/mistralai/)
      * [model2vec](https://python.langchain.com/docs/integrations/text_embedding/model2vec/)
      * [ModelScope](https://python.langchain.com/docs/integrations/text_embedding/modelscope_embedding/)
      * [MosaicML](https://python.langchain.com/docs/integrations/text_embedding/mosaicml/)
      * [Naver](https://python.langchain.com/docs/integrations/text_embedding/naver/)
      * [Nebius](https://python.langchain.com/docs/integrations/text_embedding/nebius/)
      * [Netmind](https://python.langchain.com/docs/integrations/text_embedding/netmind/)
      * [NLP Cloud](https://python.langchain.com/docs/integrations/text_embedding/nlp_cloud/)
      * [Nomic](https://python.langchain.com/docs/integrations/text_embedding/nomic/)
      * [NVIDIA NIMs](https://python.langchain.com/docs/integrations/text_embedding/nvidia_ai_endpoints/)
      * [Oracle Cloud Infrastructure Generative AI](https://python.langchain.com/docs/integrations/text_embedding/oci_generative_ai/)
      * [Ollama](https://python.langchain.com/docs/integrations/text_embedding/ollama/)
      * [OpenClip](https://python.langchain.com/docs/integrations/text_embedding/open_clip/)
      * [OpenAI](https://python.langchain.com/docs/integrations/text_embedding/openai/)
      * [OpenVINO](https://python.langchain.com/docs/integrations/text_embedding/openvino/)
      * [Embedding Documents using Optimized and Quantized Embedders](https://python.langchain.com/docs/integrations/text_embedding/optimum_intel/)
      * [Oracle AI Vector Search: Generate Embeddings](https://python.langchain.com/docs/integrations/text_embedding/oracleai/)
      * [OVHcloud](https://python.langchain.com/docs/integrations/text_embedding/ovhcloud/)
      * [Pinecone Embeddings](https://python.langchain.com/docs/integrations/text_embedding/pinecone/)
      * [PredictionGuardEmbeddings](https://python.langchain.com/docs/integrations/text_embedding/predictionguard/)
      * [PremAI](https://python.langchain.com/docs/integrations/text_embedding/premai/)
      * [SageMaker](https://python.langchain.com/docs/integrations/text_embedding/sagemaker-endpoint/)
      * [SambaNovaCloud](https://python.langchain.com/docs/integrations/text_embedding/sambanova/)
      * [SambaStudio](https://python.langchain.com/docs/integrations/text_embedding/sambastudio/)
      * [Self Hosted](https://python.langchain.com/docs/integrations/text_embedding/self-hosted/)
      * [Sentence Transformers on Hugging Face](https://python.langchain.com/docs/integrations/text_embedding/sentence_transformers/)
      * [Solar](https://python.langchain.com/docs/integrations/text_embedding/solar/)
      * [SpaCy](https://python.langchain.com/docs/integrations/text_embedding/spacy_embedding/)
      * [SparkLLM Text Embeddings](https://python.langchain.com/docs/integrations/text_embedding/sparkllm/)
      * [TensorFlow Hub](https://python.langchain.com/docs/integrations/text_embedding/tensorflowhub/)
      * [Text Embeddings Inference](https://python.langchain.com/docs/integrations/text_embedding/text_embeddings_inference/)
      * [TextEmbed - Embedding Inference Server](https://python.langchain.com/docs/integrations/text_embedding/textembed/)
      * [Titan Takeoff](https://python.langchain.com/docs/integrations/text_embedding/titan_takeoff/)
      * [Together AI](https://python.langchain.com/docs/integrations/text_embedding/together/)
      * [Upstage](https://python.langchain.com/docs/integrations/text_embedding/upstage/)
      * [Volc Engine](https://python.langchain.com/docs/integrations/text_embedding/volcengine/)
      * [Voyage AI](https://python.langchain.com/docs/integrations/text_embedding/voyageai/)
      * [Xorbits inference (Xinference)](https://python.langchain.com/docs/integrations/text_embedding/xinference/)
      * [YandexGPT](https://python.langchain.com/docs/integrations/text_embedding/yandex/)
      * [ZhipuAI](https://python.langchain.com/docs/integrations/text_embedding/zhipuai/)
    * [Other](https://python.langchain.com/docs/integrations/llms/)


  * [](https://python.langchain.com/)
  * [Components](https://python.langchain.com/docs/integrations/components/)
  * [Chat models](https://python.langchain.com/docs/integrations/chat/)
  * Google Gemini


On this page
[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain/blob/master/docs/docs/integrations/chat/google_generative_ai.ipynb)[![Open on GitHub](https://img.shields.io/badge/Open%20on%20GitHub-grey?logo=github&logoColor=white)](https://github.com/langchain-ai/langchain/blob/master/docs/docs/integrations/chat/google_generative_ai.ipynb)
# ChatGoogleGenerativeAI
Access Google's Generative AI models, including the Gemini family, directly via the Gemini API or experiment rapidly using Google AI Studio. The `langchain-google-genai` package provides the LangChain integration for these models. This is often the best starting point for individual developers.
For information on the latest models, their features, context windows, etc. head to the [Google AI docs](https://ai.google.dev/gemini-api/docs/models/gemini). All examples use the `gemini-2.0-flash` model. Gemini 2.5 Pro and 2.5 Flash can be used via `gemini-2.5-pro-preview-03-25` and `gemini-2.5-flash-preview-04-17`. All model ids can be found in the [Gemini API docs](https://ai.google.dev/gemini-api/docs/models).
### Integration details[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#integration-details "Direct link to Integration details")
Class| Package| Local| Serializable| [JS support](https://js.langchain.com/docs/integrations/chat/google_generativeai)| Package downloads| Package latest  
---|---|---|---|---|---|---  
[ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html)| [langchain-google-genai](https://python.langchain.com/api_reference/google_genai/index.html)| ❌| beta| ✅| ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-google-genai?style=flat-square&label=%20)| ![PyPI - Version](https://img.shields.io/pypi/v/langchain-google-genai?style=flat-square&label=%20)  
### Model features[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#model-features "Direct link to Model features")
[Tool calling](https://python.langchain.com/docs/how_to/tool_calling/)| [Structured output](https://python.langchain.com/docs/how_to/structured_output/)| JSON mode| [Image input](https://python.langchain.com/docs/how_to/multimodal_inputs/)| Audio input| Video input| [Token-level streaming](https://python.langchain.com/docs/how_to/chat_streaming/)| Native async| [Token usage](https://python.langchain.com/docs/how_to/chat_token_usage_tracking/)| [Logprobs](https://python.langchain.com/docs/how_to/logprobs/)  
---|---|---|---|---|---|---|---|---|---  
✅| ✅| ❌| ✅| ✅| ✅| ✅| ✅| ✅| ❌  
### Setup[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#setup "Direct link to Setup")
To access Google AI models you'll need to create a Google Account, get a Google AI API key, and install the `langchain-google-genai` integration package.
**1. Installation:**
```
%pip install -U langchain-google-genai
```

**2. Credentials:**
Head to <https://ai.google.dev/gemini-api/docs/api-key> (or via Google AI Studio) to generate a Google AI API key.
### Chat Models[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#chat-models "Direct link to Chat Models")
Use the `ChatGoogleGenerativeAI` class to interact with Google's chat models. See the [API reference](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html) for full details.
```
import getpassimport osif"GOOGLE_API_KEY"notin os.environ:  os.environ["GOOGLE_API_KEY"]= getpass.getpass("Enter your Google AI API key: ")
```

To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:
```
# os.environ["LANGSMITH_API_KEY"] = getpass.getpass("Enter your LangSmith API key: ")# os.environ["LANGSMITH_TRACING"] = "true"
```

## Instantiation[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#instantiation "Direct link to Instantiation")
Now we can instantiate our model object and generate chat completions:
```
from langchain_google_genai import ChatGoogleGenerativeAIllm = ChatGoogleGenerativeAI(  model="gemini-2.0-flash",  temperature=0,  max_tokens=None,  timeout=None,  max_retries=2,# other params...)
```

**API Reference:**[ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html)
## Invocation[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#invocation "Direct link to Invocation")
```
messages =[("system","You are a helpful assistant that translates English to French. Translate the user sentence.",),("human","I love programming."),]ai_msg = llm.invoke(messages)ai_msg
```

```
AIMessage(content="J'adore la programmation.", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run-3b28d4b8-8a62-4e6c-ad4e-b53e6e825749-0', usage_metadata={'input_tokens': 20, 'output_tokens': 7, 'total_tokens': 27, 'input_token_details': {'cache_read': 0}})
```

```
print(ai_msg.content)
```

```
J'adore la programmation.
```

## Chaining[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#chaining "Direct link to Chaining")
We can [chain](https://python.langchain.com/docs/how_to/sequence/) our model with a prompt template like so:
```
from langchain_core.prompts import ChatPromptTemplateprompt = ChatPromptTemplate.from_messages([("system","You are a helpful assistant that translates {input_language} to {output_language}.",),("human","{input}"),])chain = prompt | llmchain.invoke({"input_language":"English","output_language":"German","input":"I love programming.",})
```

**API Reference:**[ChatPromptTemplate](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.chat.ChatPromptTemplate.html)
```
AIMessage(content='Ich liebe Programmieren.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run-e5561c6b-2beb-4411-9210-4796b576a7cd-0', usage_metadata={'input_tokens': 15, 'output_tokens': 7, 'total_tokens': 22, 'input_token_details': {'cache_read': 0}})
```

## Multimodal Usage[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#multimodal-usage "Direct link to Multimodal Usage")
Gemini models can accept multimodal inputs (text, images, audio, video) and, for some models, generate multimodal outputs.
### Image Input[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#image-input "Direct link to Image Input")
Provide image inputs along with text using a `HumanMessage` with a list content format. The `gemini-2.0-flash` model can handle images.
```
import base64from langchain_core.messages import HumanMessagefrom langchain_google_genai import ChatGoogleGenerativeAI# Example using a public URL (remains the same)message_url = HumanMessage(  content=[{"type":"text","text":"Describe the image at the URL.",},{"type":"image_url","image_url":"https://picsum.photos/seed/picsum/200/300"},])result_url = llm.invoke([message_url])print(f"Response for URL image: {result_url.content}")# Example using a local image file encoded in base64image_file_path ="/Users/<USER>/projects/google-gemini/langchain/docs/static/img/agents_vs_chains.png"withopen(image_file_path,"rb")as image_file:  encoded_image = base64.b64encode(image_file.read()).decode("utf-8")message_local = HumanMessage(  content=[{"type":"text","text":"Describe the local image."},{"type":"image_url","image_url":f"data:image/png;base64,{encoded_image}"},])result_local = llm.invoke([message_local])print(f"Response for local image: {result_local.content}")
```

**API Reference:**[HumanMessage](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.human.HumanMessage.html) | [ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html)
Other supported `image_url` formats:
  * A Google Cloud Storage URI (`gs://...`). Ensure the service account has access.
  * A PIL Image object (the library handles encoding).


### Audio Input[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#audio-input "Direct link to Audio Input")
Provide audio file inputs along with text. Use a model like `gemini-2.0-flash`.
```
import base64from langchain_core.messages import HumanMessage# Ensure you have an audio file named 'example_audio.mp3' or provide the correct path.audio_file_path ="example_audio.mp3"audio_mime_type ="audio/mpeg"withopen(audio_file_path,"rb")as audio_file:  encoded_audio = base64.b64encode(audio_file.read()).decode("utf-8")message = HumanMessage(  content=[{"type":"text","text":"Transcribe the audio."},{"type":"media","data": encoded_audio,# Use base64 string directly"mime_type": audio_mime_type,},])response = llm.invoke([message])# Uncomment to runprint(f"Response for audio: {response.content}")
```

**API Reference:**[HumanMessage](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.human.HumanMessage.html)
### Video Input[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#video-input "Direct link to Video Input")
Provide video file inputs along with text. Use a model like `gemini-2.0-flash`.
```
import base64from langchain_core.messages import HumanMessagefrom langchain_google_genai import ChatGoogleGenerativeAI# Ensure you have a video file named 'example_video.mp4' or provide the correct path.video_file_path ="example_video.mp4"video_mime_type ="video/mp4"withopen(video_file_path,"rb")as video_file:  encoded_video = base64.b64encode(video_file.read()).decode("utf-8")message = HumanMessage(  content=[{"type":"text","text":"Describe the first few frames of the video."},{"type":"media","data": encoded_video,# Use base64 string directly"mime_type": video_mime_type,},])response = llm.invoke([message])# Uncomment to runprint(f"Response for video: {response.content}")
```

**API Reference:**[HumanMessage](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.human.HumanMessage.html) | [ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html)
### Image Generation (Multimodal Output)[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#image-generation-multimodal-output "Direct link to Image Generation \(Multimodal Output\)")
The `gemini-2.0-flash` model can generate text and images inline (image generation is experimental). You need to specify the desired `response_modalities`.
```
import base64from IPython.display import Image, displayfrom langchain_core.messages import AIMessagefrom langchain_google_genai import ChatGoogleGenerativeAIllm = ChatGoogleGenerativeAI(model="models/gemini-2.0-flash-preview-image-generation")message ={"role":"user","content":"Generate a photorealistic image of a cuddly cat wearing a hat.",}response = llm.invoke([message],  generation_config=dict(response_modalities=["TEXT","IMAGE"]),)def_get_image_base64(response: AIMessage)->None:  image_block =next(    blockfor block in response.contentifisinstance(block,dict)and block.get("image_url"))return image_block["image_url"].get("url").split(",")[-1]image_base64 = _get_image_base64(response)display(Image(data=base64.b64decode(image_base64), width=300))
```

**API Reference:**[AIMessage](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.AIMessage.html) | [ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html)
![](https://python.langchain.com/docs/integrations/chat/google_generative_ai/)
### Image and text to image[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#image-and-text-to-image "Direct link to Image and text to image")
You can iterate on an image in a multi-turn conversation, as shown below:
```
next_message ={"role":"user","content":"Can you take the same image and make the cat black?",}response = llm.invoke([message, response, next_message],  generation_config=dict(response_modalities=["TEXT","IMAGE"]),)image_base64 = _get_image_base64(response)display(Image(data=base64.b64decode(image_base64), width=300))
```

![](https://python.langchain.com/docs/integrations/chat/google_generative_ai/)
You can also represent an input image and query in a single message by encoding the base64 data in the [data URI scheme](https://en.wikipedia.org/wiki/Data_URI_scheme):
```
message ={"role":"user","content":[{"type":"text","text":"Can you make this cat orange?",},{"type":"image_url","image_url":{"url":f"data:image/png;base64,{image_base64}"},},],}response = llm.invoke([message],  generation_config=dict(response_modalities=["TEXT","IMAGE"]),)image_base64 = _get_image_base64(response)display(Image(data=base64.b64decode(image_base64), width=300))
```

![](https://python.langchain.com/docs/integrations/chat/google_generative_ai/)
You can also use LangGraph to manage the conversation history for you as in [this tutorial](https://python.langchain.com/docs/tutorials/chatbot/).
## Tool Calling[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#tool-calling "Direct link to Tool Calling")
You can equip the model with tools to call.
```
from langchain_core.tools import toolfrom langchain_google_genai import ChatGoogleGenerativeAI# Define the tool@tool(description="Get the current weather in a given location")defget_weather(location:str)->str:return"It's sunny."# Initialize the model and bind the toolllm = ChatGoogleGenerativeAI(model="gemini-2.0-flash")llm_with_tools = llm.bind_tools([get_weather])# Invoke the model with a query that should trigger the toolquery ="What's the weather in San Francisco?"ai_msg = llm_with_tools.invoke(query)# Check the tool calls in the responseprint(ai_msg.tool_calls)# Example tool call message would be needed here if you were actually running the toolfrom langchain_core.messages import ToolMessagetool_message = ToolMessage(  content=get_weather(*ai_msg.tool_calls[0]["args"]),  tool_call_id=ai_msg.tool_calls[0]["id"],)llm_with_tools.invoke([ai_msg, tool_message])# Example of passing tool result back
```

**API Reference:**[tool](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.convert.tool.html) | [ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html) | [ToolMessage](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.ToolMessage.html)
```
[{'name': 'get_weather', 'args': {'location': 'San Francisco'}, 'id': 'a6248087-74c5-4b7c-9250-f335e642927c', 'type': 'tool_call'}]
```

```
AIMessage(content="OK. It's sunny in San Francisco.", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run-ac5bb52c-e244-4c72-9fbc-fb2a9cd7a72e-0', usage_metadata={'input_tokens': 29, 'output_tokens': 11, 'total_tokens': 40, 'input_token_details': {'cache_read': 0}})
```

## Structured Output[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#structured-output "Direct link to Structured Output")
Force the model to respond with a specific structure using Pydantic models.
```
from langchain_core.pydantic_v1 import BaseModel, Fieldfrom langchain_google_genai import ChatGoogleGenerativeAI# Define the desired structureclassPerson(BaseModel):"""Information about a person."""  name:str= Field(..., description="The person's name")  height_m:float= Field(..., description="The person's height in meters")# Initialize the modelllm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0)structured_llm = llm.with_structured_output(Person)# Invoke the model with a query asking for structured informationresult = structured_llm.invoke("Who was the 16th president of the USA, and how tall was he in meters?")print(result)
```

**API Reference:**[ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html)
```
name='Abraham Lincoln' height_m=1.93
```

## Token Usage Tracking[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#token-usage-tracking "Direct link to Token Usage Tracking")
Access token usage information from the response metadata.
```
from langchain_google_genai import ChatGoogleGenerativeAIllm = ChatGoogleGenerativeAI(model="gemini-2.0-flash")result = llm.invoke("Explain the concept of prompt engineering in one sentence.")print(result.content)print("\nUsage Metadata:")print(result.usage_metadata)
```

**API Reference:**[ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html)
```
Prompt engineering is the art and science of crafting effective text prompts to elicit desired and accurate responses from large language models.Usage Metadata:{'input_tokens': 10, 'output_tokens': 24, 'total_tokens': 34, 'input_token_details': {'cache_read': 0}}
```

## Built-in tools[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#built-in-tools "Direct link to Built-in tools")
Google Gemini supports a variety of built-in tools ([google search](https://ai.google.dev/gemini-api/docs/grounding/search-suggestions), [code execution](https://ai.google.dev/gemini-api/docs/code-execution?lang=python)), which can be bound to the model in the usual way.
```
from google.ai.generativelanguage_v1beta.types import Tool as GenAIToolresp = llm.invoke("When is the next total solar eclipse in US?",  tools=[GenAITool(google_search={})],)print(resp.content)
```

```
The next total solar eclipse visible in the United States will occur on August 23, 2044. However, the path of totality will only pass through Montana, North Dakota, and South Dakota.For a total solar eclipse that crosses a significant portion of the continental U.S., you'll have to wait until August 12, 2045. This eclipse will start in California and end in Florida.
```

```
from google.ai.generativelanguage_v1beta.types import Tool as GenAIToolresp = llm.invoke("What is 2*2, use python",  tools=[GenAITool(code_execution={})],)for c in resp.content:ifisinstance(c,dict):if c["type"]=="code_execution_result":print(f"Code execution result: {c['code_execution_result']}")elif c["type"]=="executable_code":print(f"Executable code: {c['executable_code']}")else:print(c)
```

```
Executable code: print(2*2)Code execution result: 42*2 is 4.``````output/Users/<USER>/projects/google-gemini/langchain/.venv/lib/python3.9/site-packages/langchain_google_genai/chat_models.py:580: UserWarning:     ⚠️ Warning: Output may vary each run.     - 'executable_code': Always present.     - 'execution_result' & 'image_url': May be absent for some queries.     Validate before using in production. warnings.warn(
```

## Native Async[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#native-async "Direct link to Native Async")
Use asynchronous methods for non-blocking calls.
```
from langchain_google_genai import ChatGoogleGenerativeAIllm = ChatGoogleGenerativeAI(model="gemini-2.0-flash")asyncdefrun_async_calls():# Async invoke  result_ainvoke =await llm.ainvoke("Why is the sky blue?")print("Async Invoke Result:", result_ainvoke.content[:50]+"...")# Async streamprint("\nAsync Stream Result:")asyncfor chunk in llm.astream("Write a short poem about asynchronous programming."):print(chunk.content, end="", flush=True)print("\n")# Async batch  results_abatch =await llm.abatch(["What is 1+1?","What is 2+2?"])print("Async Batch Results:",[res.content for res in results_abatch])await run_async_calls()
```

**API Reference:**[ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html)
```
Async Invoke Result: The sky is blue due to a phenomenon called **Rayle...Async Stream Result:The thread is free, it does not wait,For answers slow, or tasks of fate.A promise made, a future bright,It moves ahead, with all its might.A callback waits, a signal sent,When data's read, or job is spent.Non-blocking code, a graceful dance,Responsive apps, a fleeting glance.Async Batch Results: ['1 + 1 = 2', '2 + 2 = 4']
```

## Safety Settings[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#safety-settings "Direct link to Safety Settings")
Gemini models have default safety settings that can be overridden. If you are receiving lots of "Safety Warnings" from your models, you can try tweaking the `safety_settings` attribute of the model. For example, to turn off safety blocking for dangerous content, you can construct your LLM as follows:
```
from langchain_google_genai import(  ChatGoogleGenerativeAI,  HarmBlockThreshold,  HarmCategory,)llm = ChatGoogleGenerativeAI(  model="gemini-1.5-pro",  safety_settings={    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,},)
```

**API Reference:**[ChatGoogleGenerativeAI](https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html) | [HarmBlockThreshold](https://python.langchain.com/api_reference/google/ai/google.ai.generativelanguage_v1beta.types.safety.HarmBlockThreshold.html) | [HarmCategory](https://python.langchain.com/api_reference/google/ai/google.ai.generativelanguage_v1beta.types.safety.HarmCategory.html)
For an enumeration of the categories and thresholds available, see Google's [safety setting types](https://ai.google.dev/api/python/google/generativeai/types/SafetySettingDict).
## API reference[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#api-reference "Direct link to API reference")
For detailed documentation of all ChatGoogleGenerativeAI features and configurations head to the API reference: <https://python.langchain.com/api_reference/google_genai/chat_models/langchain_google_genai.chat_models.ChatGoogleGenerativeAI.html>
## Related[​](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#related "Direct link to Related")
  * Chat model [conceptual guide](https://python.langchain.com/docs/concepts/chat_models/)
  * Chat model [how-to guides](https://python.langchain.com/docs/how_to/#chat-models)


[Edit this page](https://github.com/langchain-ai/langchain/edit/master/docs/docs/integrations/chat/google_generative_ai.ipynb)
#### Was this page helpful?
[PreviousGoodfire](https://python.langchain.com/docs/integrations/chat/goodfire/)[NextGoogle Cloud Vertex AI](https://python.langchain.com/docs/integrations/chat/google_vertex_ai_palm/)
  * [Integration details](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#integration-details)
  * [Model features](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#model-features)
  * [Setup](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#setup)
  * [Chat Models](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#chat-models)
  * [Instantiation](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#instantiation)
  * [Invocation](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#invocation)
  * [Chaining](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#chaining)
  * [Multimodal Usage](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#multimodal-usage)
    * [Image Input](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#image-input)
    * [Audio Input](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#audio-input)
    * [Video Input](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#video-input)
    * [Image Generation (Multimodal Output)](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#image-generation-multimodal-output)
    * [Image and text to image](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#image-and-text-to-image)
  * [Tool Calling](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#tool-calling)
  * [Structured Output](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#structured-output)
  * [Token Usage Tracking](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#token-usage-tracking)
  * [Built-in tools](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#built-in-tools)
  * [Native Async](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#native-async)
  * [Safety Settings](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#safety-settings)
  * [API reference](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#api-reference)
  * [Related](https://python.langchain.com/docs/integrations/chat/google_generative_ai/#related)


Community
  * [Twitter](https://twitter.com/LangChainAI)


GitHub
  * [Organization](https://github.com/langchain-ai)
  * [Python](https://github.com/langchain-ai/langchain)
  * [JS/TS](https://github.com/langchain-ai/langchainjs)


More
  * [Homepage](https://langchain.com)
  * [Blog](https://blog.langchain.dev)
  * [YouTube](https://www.youtube.com/@LangChain)


Copyright © 2025 LangChain, Inc.
