from typing import Optional
from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any
import os

class WriteFileArgs(BaseModel):
    file_path: str = Field(
        ...,
        description="The absolute path to the file to write to (e.g., '/home/<USER>/project/file.txt'). Relative paths are not supported."
    )
    content: str = Field(
        ...,
        description="The content to write to the file."
    )

async def run_write_file(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Writes content to a specified file in the local filesystem.
    """
    parsed = WriteFileArgs.model_validate_json(args)
    file_path = parsed.file_path
    content = parsed.content

    if not os.path.isabs(file_path):
        return f"Error: File path must be absolute: {file_path}"

    try:
        dir_name = os.path.dirname(file_path)
        if not os.path.exists(dir_name):
            os.makedirs(dir_name, exist_ok=True)

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return f"Successfully wrote to file: {file_path}"
    except Exception as e:
        return f"Error writing to file: {e}"

write_file_tool = FunctionTool(
    name="write_file",
    description="Writes content to a specified file in the local filesystem.",
    params_json_schema=WriteFileArgs.model_json_schema(),
    on_invoke_tool=run_write_file,
)
