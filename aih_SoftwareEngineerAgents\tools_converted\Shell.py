from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any, Optional
import subprocess
import os

class ShellArgs(BaseModel):
    command: str = Field(
        ...,
        description="Exact bash command to execute as `bash -c <command>`"
    )
    directory: Optional[str] = Field(
        None,
        description="(OPTIONAL) Directory to run the command in, if not the project root directory. Must be relative to the project root directory and must already exist."
    )
    description: Optional[str] = Field(
        None,
        description="Brief description of the command for the user. Be specific and concise. Ideally a single sentence. Can be up to 3 sentences for clarity. No line breaks."
    )

async def run_shell_command(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    This tool executes a given shell command as `bash -c <command>`.
    """
    parsed = ShellArgs.model_validate_json(args)
    command = parsed.command
    directory = parsed.directory

    try:
        process = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=directory,
            check=False
        )
        output = f"Command: {command}\n"
        if directory:
            output += f"Directory: {directory}\n"
        output += f"Exit Code: {process.returncode}\n"
        if process.stdout:
            output += f"Stdout:\n{process.stdout}"
        if process.stderr:
            output += f"Stderr:\n{process.stderr}"
        return output

    except FileNotFoundError:
        return f"Error: The directory '{directory}' does not exist."
    except Exception as e:
        return f"An unexpected error occurred: {e}"

shell_tool = FunctionTool(
    name="run_shell_command",
    description='This tool executes a given shell command as `bash -c <command>`',
    params_json_schema=ShellArgs.model_json_schema(),
    on_invoke_tool=run_shell_command,
)