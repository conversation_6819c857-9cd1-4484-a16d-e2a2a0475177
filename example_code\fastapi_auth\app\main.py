from fastapi import FastAPI
from app.api.endpoints import users, auth, items
from app.db.database import connect_to_mongo, close_mongo_connection

app = FastAPI()

@app.on_event("startup")
async def startup_event():
    await connect_to_mongo()

@app.on_event("shutdown")
async def shutdown_event():
    await close_mongo_connection()

app.include_router(users.router, prefix="/users", tags=["users"])
app.include_router(auth.router, prefix="/auth", tags=["auth"])
app.include_router(items.router, tags=["items"])

@app.get("/")
async def root():
    return {"message": "Hello World"}
