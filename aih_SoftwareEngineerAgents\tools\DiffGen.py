from pydantic import BaseModel, Field
from agents import FunctionT<PERSON>, RunContextWrap<PERSON>, Agent, Runner
from typing import Any, Optional, List
import os
import re
import difflib
from concurrent.futures import ThreadPoolExecutor
import asyncio

class DiffArgs(BaseModel):
    file_path: str = Field(..., description="Path to the file")
    new_content: str = Field(..., description="Proposed new content")

async def generate_diff(ctx: RunContextWrapper[Any], args: str) -> str:
    """Generate a diff between current file and proposed changes"""
    parsed = DiffArgs.model_validate_json(args)
    
    try:
        with open(parsed.file_path, 'r', encoding='utf-8') as f:
            original = f.read().splitlines()
        
        proposed = parsed.new_content.splitlines()
        
        diff = difflib.unified_diff(
            original,
            proposed,
            fromfile=parsed.file_path,
            tofile="proposed",
            lineterm=""
        )
        
        return "\n".join(diff)
    
    except Exception as e:
        return f"Diff generation failed: {e}"

diff_tool = FunctionTool(
    name="generate_diff",
    description="Generate difference between current file and proposed changes",
    params_json_schema=DiffArgs.model_json_schema(),
    on_invoke_tool=generate_diff
)