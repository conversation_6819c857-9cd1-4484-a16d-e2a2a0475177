from agents import Agent, <PERSON>, Run<PERSON>onfig, CodeInterpreterTool
from agents.extensions.models.litellm_model import LitellmModel
from aih_agents import CodeWriter, Planner, FileEditor
from utils.usages import extract_agent_usage
import json
import asyncio
from openai.types.shared import Reasoning, ReasoningEffort
from typing import List


async def main():
    prompt = """ You will help user to do his tasks
                    only take littele inputs dont ask too much questios/validaitons
    """

    # Initialize and orchestrate
    file_editor_agent = FileEditor.file_editor_agent
    code_writer_agent = CodeWriter.code_writer_agent
    planner_agent = Planner.planner_agent

    planner_agent.tools.extend(
        [
            code_writer_agent.as_tool(
                tool_name="Coding_Agent", tool_description="This is coding agent."
            ),
            file_editor_agent.as_tool(
                tool_name="File_Editor_Agent",
                tool_description="This is file editor agent that can manipulate files.",
            ),
        ]
    )

    results = await Runner.run(
        planner_agent,
        prompt,
        run_config=RunConfig(
            workflow_name="AIH project builder",
        ),
        max_turns=50,
    )

    # Save results
    # with open("results.txt", "w") as f:
    #     f.write(str(results))

    usage_data = extract_agent_usage(results)
    with open("results.json", "w") as f:
        json.dump(usage_data, f, indent=2)


if __name__ == "__main__":
    asyncio.run(main())
