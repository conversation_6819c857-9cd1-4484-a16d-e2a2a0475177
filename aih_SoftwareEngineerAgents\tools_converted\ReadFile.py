from typing import Optional
from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any
import os

class ReadFileArgs(BaseModel):
    absolute_path: str = Field(
        ...,
        description="The absolute path to the file to read (e.g., '/home/<USER>/project/file.txt'). Relative paths are not supported. You must provide an absolute path."
    )
    offset: Optional[int] = Field(
        None,
        description="Optional: For text files, the 0-based line number to start reading from. Requires 'limit' to be set. Use for paginating through large files."
    )
    limit: Optional[int] = Field(
        None,
        description="Optional: For text files, maximum number of lines to read. Use with 'offset' to paginate through large files. If omitted, reads the entire file (if feasible, up to a default limit)."
    )

async def run_read_file(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Reads and returns the content of a specified file from the local filesystem.
    """
    parsed = ReadFileArgs.model_validate_json(args)
    absolute_path = parsed.absolute_path
    offset = parsed.offset
    limit = parsed.limit

    if not os.path.isabs(absolute_path):
        return f"Error: File path must be absolute: {absolute_path}"

    try:
        with open(absolute_path, 'r', encoding='utf-8') as f:
            if offset is not None and limit is not None:
                lines = f.readlines()
                sliced_lines = lines[offset:offset + limit]
                return "".join(sliced_lines)
            else:
                return f.read()
    except FileNotFoundError:
        return f"Error: File not found at {absolute_path}"
    except Exception as e:
        return f"Error reading file: {e}"

read_file_tool = FunctionTool(
    name="read_file",
    description='Reads and returns the content of a specified file from the local filesystem. Handles text, images (PNG, JPG, GIF, WEBP, SVG, BMP), and PDF files. For text files, it can read specific line ranges.',
    params_json_schema=ReadFileArgs.model_json_schema(),
    on_invoke_tool=run_read_file,
)