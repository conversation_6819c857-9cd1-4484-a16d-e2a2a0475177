from pydantic import BaseModel, Field
from agents import <PERSON>ctionTool, RunContextWrapper
from typing import Any
import os

class MemoryToolArgs(BaseModel):
    fact: str = Field(
        ...,
        description="The specific fact or piece of information to remember. Should be a clear, self-contained statement."
    )

async def run_save_memory(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Saves a specific piece of information or fact to your long-term memory.
    """
    parsed = MemoryToolArgs.model_validate_json(args)
    fact = parsed.fact

    try:
        # Simplified memory storage in a local file
        memory_file = os.path.join(os.path.expanduser("~"), ".gemini_memory.txt")
        with open(memory_file, "a+") as f:
            f.write(f"{fact}\n")
        return f"Okay, I've remembered that: '{fact}'"
    except Exception as e:
        return f"Error saving memory: {e}"

memory_tool = FunctionTool(
    name="save_memory",
    description=(
        'Saves a specific piece of information or fact to your long-term memory.\n'
        'Use this tool:\n'
        '- When the user explicitly asks you to remember something (e.g., "Remember that I like pineapple on pizza", "Please save this: my cat\'s name is <PERSON><PERSON><PERSON>").\n'
        '- When the user states a clear, concise fact about themselves, their preferences, or their environment that seems important for you to retain for future interactions to provide a more personalized and effective assistance.\n\n'
        'Do NOT use this tool:\n'
        '- To remember conversational context that is only relevant for the current session.\n'
        '- To save long, complex, or rambling pieces of text. The fact should be relatively short and to the point.\n'
        '- If you are unsure whether the information is a fact worth remembering long-term. If in doubt, you can ask the user, "Should I remember that for you?"\n\n'
        '## Parameters\n\n'
        '- `fact` (string, required): The specific fact or piece of information to remember. This should be a clear, self-contained statement. For example, if the user says "My favorite color is blue", the fact would be "My favorite color is blue".'
    ),
    params_json_schema=MemoryToolArgs.model_json_schema(),
    on_invoke_tool=run_save_memory,
)