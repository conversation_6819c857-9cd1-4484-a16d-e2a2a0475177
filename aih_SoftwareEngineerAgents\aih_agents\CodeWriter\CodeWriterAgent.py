from agents import Agent
from agents.extensions.models.litellm_model import LitellmModel
from dotenv import load_dotenv
import os
from agents import enable_verbose_stdout_logging, ModelSettings
from tools import * # Your existing tools module
from openai.types.shared import Reasoning,ReasoningEffort

# Initialize environment
enable_verbose_stdout_logging()
load_dotenv()

# Configuration
api_key = os.getenv("API_Key")
endpoint = os.getenv("End_point")
deployment_name = os.getenv("Deployment_name")
model = "azure/nexa1-gpt-4.1-global-std"

# Enhanced Toolset for Code Writing
code_writer_agent = Agent(
    name="AIH Code Writer",
    model=LitellmModel(
        model=model,
        base_url=endpoint,
        api_key=api_key,
    ),
    tools=[
        # Existing Tools
       CodeBaseSearch.codebase_search_tool,
    #    DeleteFile.delete_file_tool,
       DiffGen.diff_tool,
       FileAnalysis.file_analysis_tool,
       FileEdit.file_edit_tool,
      #  FileSearch.file_search_tool,
       GrepSearch.grep_search_tool,
       ListDirectories.list_dir_tool,
    #    ReadFile.read_file_tool,
       ReApply.reapply_tool,
    #    RunCMDCommands.run_terminal_cmd_tool,
    #    WebSearch.web_search_tool,
       WriteToFile.write_file_tool,
    #    CLIInput.cli_input_tool,
        
        # Suggested Additional Tools (would need implementation)
    #    TestGenerator.test_gen_tool,        # New - Auto-generate unit tests
    #    DependencyManager.dep_tool,        # New - Manage package dependencies
    #    CodeReview.review_tool,            # New - Static code analysis
    #    APIClient.api_client_tool,         # New - Make API calls
    #     templates.TemplateGenerator.template_tool # New - Scaffold projects
    ],
    instructions="""You are an AI Code Writer specializing in full-stack development. Your capabilities include:

1. CODE GENERATION:
   - Write production-ready code in multiple languages
   - Implement complete features from specifications
   - Generate boilerplate and scaffolding code

2. CODE OPTIMIZATION:
   - Refactor existing code
   - Improve performance
   - Apply best practices


Always:
1. Validate requirements before implementation
2. Include clear comments
3. Follow PEP8/ESLint standards where applicable
4. Generate matching test cases
5. Output complete file contents with proper paths
""",
    model_settings=ModelSettings(
        temperature=0.2,  # Lower for precise code
        top_p=0.3,
        frequency_penalty=0.1,
        presence_penalty=0.1,
        tool_choice="required",
        parallel_tool_calls=3,
        max_tokens=4000,  # For longer code files
        # reasoning=Reasoning(effort='high'),
        include_usage=True,
    ),
)
