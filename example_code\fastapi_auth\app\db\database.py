from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import MongoClient
import os
from dotenv import load_dotenv

load_dotenv()

MONGODB_URL = os.getenv("MONGODB_URL")
MONGODB_DATABASE = os.getenv("MONGODB_DATABASE")


if not MONGODB_URL:
    raise ValueError("MONGODB_URL is not set in the environment variables")

if not MONGODB_DATABASE:
    raise ValueError("MONGODB_DATABASE is not set in the environment variables")



async def connect_to_mongo():
    global client, database
    client = AsyncIOMotorClient(MONGODB_URL)
    database = client[MONGODB_DATABASE]
    print("Connected to MongoDB")


async def close_mongo_connection():
    client.close()
    print("Disconnected from MongoDB")


def get_database():
    return database


# For synchronous operations (e.g., initial setup or migrations)
sync_client = MongoClient(MONGODB_URL)
sync_database = sync_client[MONGODB_DATABASE]

def get_sync_database():
    return sync_database
