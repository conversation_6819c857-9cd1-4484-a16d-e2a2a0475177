from typing import Optional
from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any
import requests

class WebFetchArgs(BaseModel):
    prompt: str = Field(
        ...,
        description="A comprehensive prompt that includes the URL(s) (up to 20) to fetch and specific instructions on how to process their content (e.g., \"Summarize https://example.com/article and extract key points from https://another.com/data\"). Must contain as least one URL starting with http:// or https://."
    )

async def run_web_fetch(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Processes content from URL(s), including local and private network addresses (e.g., localhost), embedded in a prompt.
    """
    parsed = WebFetchArgs.model_validate_json(args)
    prompt = parsed.prompt

    # Simple URL extraction (can be improved with regex for robustness)
    urls = [word for word in prompt.split() if word.startswith("http://") or word.startswith("https://")]

    if not urls:
        return "Error: No URL found in the prompt."

    # For simplicity, process only the first URL found
    url = urls[0]

    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # Raise an exception for HTTP errors
        return f"Content from {url}:\n{response.text[:1000]}..."
    except requests.exceptions.RequestException as e:
        return f"Error fetching content from {url}: {e}"

web_fetch_tool = FunctionTool(
    name="web_fetch",
    description="Processes content from URL(s), including local and private network addresses (e.g., localhost), embedded in a prompt. Include up to 20 URLs and instructions (e.g., summarize, extract specific data) directly in the 'prompt' parameter.",
    params_json_schema=WebFetchArgs.model_json_schema(),
    on_invoke_tool=run_web_fetch,
)
