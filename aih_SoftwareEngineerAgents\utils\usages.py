from collections import defaultdict

def extract_agent_usage(run_result):
    agent_usage = defaultdict(
        lambda: {
            "requests": 0,
            "input_tokens": 0,
            "output_tokens": 0,
            "total_tokens": 0,
            "cached_tokens": 0,
        }
    )

    for response in run_result.raw_responses:
        # Get the agent name (assuming single agent in this case)
        agent_name = (
            run_result._last_agent.name if run_result._last_agent else "Unknown"
        )

        # Update usage stats
        agent_usage[agent_name]["requests"] += response.usage.requests
        agent_usage[agent_name]["input_tokens"] += response.usage.input_tokens
        agent_usage[agent_name]["output_tokens"] += response.usage.output_tokens
        agent_usage[agent_name]["total_tokens"] += response.usage.total_tokens

        # Get cached tokens if available
        if hasattr(response.usage.input_tokens_details, "cached_tokens"):
            agent_usage[agent_name]["cached_tokens"] += (
                response.usage.input_tokens_details.cached_tokens
            )

    return dict(agent_usage)
