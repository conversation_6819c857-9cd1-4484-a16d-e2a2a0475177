from pydantic import BaseModel, Field
from typing import List
from agents import FunctionT<PERSON>, RunContextWrapper
from typing import Any

class FetchRulesArgs(BaseModel):
    rule_names: List[str] = Field(..., description="Names of rules to fetch.")

async def run_fetch_rules(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
async def run_fetch_rules(
    ctx: RunContextWrapper[Any],
    args: FetchRulesArgs
) -> str:
    """
    Fetch navigation rules provided by the user for codebase context.
    """
    print(f"Fetching rules: {args.rule_names}")
    # TODO: Implement actual rule fetching logic here
    return f"Successfully attempted to fetch rules: {args.rule_names}"
fetch_rules_tool = FunctionTool(
    name="fetch_rules",
    description="Fetch navigation rules provided by the user for codebase context.",
    params_json_schema=FetchRulesArgs.model_json_schema(),
    on_invoke_tool=run_fetch_rules,
)
