from pydantic import BaseModel, <PERSON>
from typing import Optional
from agents import FunctionTool, RunContextWrapper
from typing import Any
import subprocess

class RunTerminalCmdArgs(BaseModel):
    command: str = Field(..., description="The Command Prompt (CMD only) command to execute.")
    is_background: bool = Field(False, description="Whether to run in background.")
    require_user_approval: bool = Field(True, description="Whether user approval is needed.")

async def run_run_terminal_cmd(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Propose and optionally run a CMD command on the user's system.
    """
    parsed = RunTerminalCmdArgs.model_validate_json(args)
    command = parsed.command
    is_background = parsed.is_background
    require_user_approval = parsed.require_user_approval
    if require_user_approval:
        return (
            f"User approval required to run command: '{command}'. "
            f"Background: {is_background}."
        )
    try:
        if is_background:
            process = subprocess.Popen(command, shell=True,
                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return f"Command '{command}' started in background with PID {process.pid}."
        else:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            output = result.stdout.strip()
            error = result.stderr.strip()
            if result.returncode == 0:
                return f"Command output:\n{output}" if output else "Command executed successfully with no output."
            else:
                return f"Command failed with error:\n{error}" if error else f"Command failed with return code {result.returncode}."
    except Exception as e:
        return f"Error running command '{command}': {e}"

run_terminal_cmd_tool = FunctionTool(
    name="run_terminal_cmd",
    description="Propose and optionally run a terminal command on the user's system.",
    params_json_schema=RunTerminalCmdArgs.model_json_schema(),
    on_invoke_tool=run_run_terminal_cmd,
)
