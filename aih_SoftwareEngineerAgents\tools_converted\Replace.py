from typing import Optional
from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any
import os

class ReplaceArgs(BaseModel):
    file_path: str = Field(
        ...,
        description="The absolute path to the file to modify. Must start with '/'."
    )
    old_string: str = Field(
        ...,
        description="The exact literal text to replace, preferably unescaped. For single replacements (default), include at least 3 lines of context BEFORE and AFTER the target text, matching whitespace and indentation precisely. For multiple replacements, specify expected_replacements parameter. If this string is not the exact literal text (i.e. you escaped it) or does not match exactly, the tool will fail."
    )
    new_string: str = Field(
        ...,
        description="The exact literal text to replace `old_string` with, preferably unescaped. Provide the EXACT text. Ensure the resulting code is correct and idiomatic."
    )
    expected_replacements: Optional[int] = Field(
        1,
        description="Number of replacements expected. Defaults to 1 if not specified. Use when you want to replace multiple occurrences."
    )

async def run_replace(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Replaces text within a file.
    """
    parsed = ReplaceArgs.model_validate_json(args)
    file_path = parsed.file_path
    old_string = parsed.old_string
    new_string = parsed.new_string
    expected_replacements = parsed.expected_replacements

    if not os.path.isabs(file_path):
        return f"Error: File path must be absolute: {file_path}"

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        if old_string == "":
            # If old_string is empty, it means we are creating a new file or appending
            if os.path.exists(file_path):
                return f"Error: File already exists at {file_path}. Cannot create a new file with an empty old_string if the file already exists."
            else:
                # Create parent directories if they don't exist
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_string)
                return f"Successfully created new file: {file_path}"

        # Perform replacement
        if expected_replacements == 1:
            new_content, num_replacements = content.replace(old_string, new_string, 1), 1
        else:
            new_content, num_replacements = content.replace(old_string, new_string), content.count(old_string)

        if num_replacements == 0:
            return f"Error: Could not find the string to replace in {file_path}. No replacements made."
        elif expected_replacements is not None and num_replacements != expected_replacements:
            return f"Error: Expected {expected_replacements} replacement(s) but found {num_replacements} in {file_path}. No replacements made."

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)

        return f"Successfully replaced {num_replacements} occurrence(s) in {file_path}"

    except FileNotFoundError:
        return f"Error: File not found at {file_path}"
    except Exception as e:
        return f"Error during replacement: {e}"

replace_tool = FunctionTool(
    name="replace",
    description="Replaces text within a file. By default, replaces a single occurrence, but can replace multiple occurrences when `expected_replacements` is specified. This tool requires providing significant context around the change to ensure precise targeting. Always use the read_file tool to examine the file's current content before attempting a text replacement.",
    params_json_schema=ReplaceArgs.model_json_schema(),
    on_invoke_tool=run_replace,
)
