from aih_agents import file_modifier_agent
from agents import Runner


async def main():
 
    results = await Runner.run(file_modifier_agent,"Task: i have a scirpt to check number is prime, you need to refactor it, C:/Users/<USER>/OneDrive/Desktop/A<PERSON><PERSON> kuma<PERSON>/forks/nseit_code_agent/primes.py  i dont now why i am failing can you please check it and fix it and update the scriptS.",max_turns=15)
    print(results)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())