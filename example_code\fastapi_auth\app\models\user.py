from pydantic import BaseModel, Field
from typing import Optional

class User(BaseModel):
    id: Optional[str] = Field(default=None, alias="_id")
    username: str = Field(...)
    email: str = Field(...)
    password: str = Field(...)

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True  # Allows non-pydantic types
        json_encoders = {
            objectid: str
        }

class UserCreate(BaseModel):
    username: str = Field(...)
    email: str = Field(...)
    password: str = Field(...)

class UserLogin(BaseModel):
    username: str = Field(...)
    password: str = Field(...)

class UserResponse(BaseModel):
    id: str
    username: str
    email: str
