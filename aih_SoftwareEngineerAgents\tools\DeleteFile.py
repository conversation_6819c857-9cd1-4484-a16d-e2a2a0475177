from pydantic import BaseModel, <PERSON>
from typing import Optional
from agents import FunctionTool, RunContextWrapper
from typing import Any, List
import os

class DeleteFileArgs(BaseModel):
    target_files: List[str] = Field(..., description="List of Paths to the files to delete.")

async def run_delete_file(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Delete a file gracefully if it exists.
    """
    parsed = DeleteFileArgs.model_validate_json(args)
    target_files = parsed.target_files
    deleted_files = []
    failed_files = []
    for target_file in target_files:
        try:
            os.remove(target_file)
            deleted_files.append(target_file)
        except Exception as e:
            failed_files.append(f"Failed to delete: {target_file}, Error: {e}")
            
    return f"Successfully deleted {deleted_files}."

delete_file_tool = FunctionTool(
    name="delete_files",
    description="Delete files gracefully if it exists.",
    params_json_schema=DeleteFileArgs.model_json_schema(),
    on_invoke_tool=run_delete_file,
)
