from typing import Optional
from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any
import os
import re

class GrepArgs(BaseModel):
    pattern: str = Field(
        ...,
        description="The regular expression (regex) pattern to search for within file contents (e.g., 'function\s+myFunction', 'import\s+\{.*\}\s+from\s+.*')."
    )
    path: Optional[str] = Field(
        None,
        description='Optional: The absolute path to the directory to search within. If omitted, searches the current working directory.'
    )
    include: Optional[str] = Field(
        None,
        description="Optional: A glob pattern to filter which files are searched (e.g., '*.js', '*.{ts,tsx}', 'src/**'). If omitted, searches all files (respecting potential global ignores)."
    )

async def run_grep(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Searches for a regular expression pattern within the content of files.
    """
    parsed = GrepArgs.model_validate_json(args)
    pattern = parsed.pattern
    search_path = parsed.path or "."
    include = parsed.include

    if not os.path.isabs(search_path):
        return f"Error: Search path must be absolute: {search_path}"

    matches = []
    for root, _, files in os.walk(search_path):
        for file in files:
            if include and not fnmatch.fnmatch(file, include):
                continue
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        if re.search(pattern, line):
                            matches.append(f"{file_path}:{line_num}:{line.strip()}")
            except Exception as e:
                print(f"Error reading file {file_path}: {e}")

    if not matches:
        return "No matches found."

    return "\n".join(matches)

grep_tool = FunctionTool(
    name="search_file_content",
    description='Searches for a regular expression pattern within the content of files in a specified directory (or current working directory). Can filter files by a glob pattern. Returns the lines containing matches, along with their file paths and line numbers.',
    params_json_schema=GrepArgs.model_json_schema(),
    on_invoke_tool=run_grep,
)
