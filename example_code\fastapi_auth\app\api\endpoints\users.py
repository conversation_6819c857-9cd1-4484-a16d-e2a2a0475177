from fastapi import APIRouter, HTTPException, Depends
from app.models.user import UserCreate, UserResponse
from app.core.security import get_password_hash
from app.db.database import get_database
from pymongo.errors import DuplicateKeyError
from bson import ObjectId

router = APIRouter()

@router.post("/register", response_model=UserResponse)
async def register_user(user: UserCreate, db = Depends(get_database)):
    hashed_password = get_password_hash(user.password)
    user_dict = user.dict()
    user_dict["password"] = hashed_password
    try:
        result = await db.users.insert_one(user_dict)
        created_user = await db.users.find_one({"_id": result.inserted_id})
        return UserResponse(**created_user, id=str(created_user["_id"]))
    except DuplicateKeyError:
        raise HTTPException(status_code=400, detail="Username or email already registered")
