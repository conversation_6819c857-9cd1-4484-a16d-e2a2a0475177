import asyncio
from playwright.async_api import async_playwright
from crawl4ai import AsyncWebCrawler
import json
import os
from datetime import datetime
import time


class GoogleSearchScraper:
    def __init__(self, headless=False, slow_mo=1000):
        self.headless = headless
        self.slow_mo = slow_mo  # Milliseconds delay between actions
        self.search_results = []

    async def run_complete_workflow(self):
        """Complete workflow: Google search -> scrape first 2 results"""
        print("🚀 Starting complete Google search and scraping workflow...")

        # Step 1: Search Google and get URLs
        search_urls = await self.search_google_and_get_urls()

        if not search_urls:
            print("❌ No search results found!")
            return

        print(f"✅ Found {len(search_urls)} search results")

        # Step 2: Scrape the URLs using crawl4ai
        scraped_data = await self.scrape_urls_with_crawl4ai(
            search_urls[:2]
        )  # First 2 results

        # Step 3: Save results
        await self.save_results(scraped_data)

        return scraped_data

    async def search_google_and_get_urls(self, search_query: str):
        """Open browser, search Google, extract first 2 result URLs"""
        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(
                headless=self.headless,
                slow_mo=self.slow_mo,
                args=[
                    "--no-sandbox",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-dev-shm-usage",
                ],
            )

            # Create context and page
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
                viewport={"width": 1920, "height": 1080},
            )

            page = await context.new_page()

            try:
                print("🔍 Opening Google...")
                await page.goto("https://www.google.com", wait_until="networkidle")

                # Handle cookie consent if it appears
                try:
                    await page.wait_for_selector(
                        'button:has-text("Accept all")', timeout=3000
                    )
                    await page.click('button:has-text("Accept all")')
                    print("✅ Accepted cookies")
                except:
                    try:
                        await page.wait_for_selector(
                            'button:has-text("I agree")', timeout=3000
                        )
                        await page.click('button:has-text("I agree")')
                        print("✅ Accepted cookies (alternative)")
                    except:
                        print("ℹ️ No cookie consent found or already handled")

                # Find search box and search
                print(f"🔍 Searching for: '{search_query}'")

                # Wait for search box and type query
                await page.wait_for_selector(
                    'textarea[name="q"], input[name="q"]', timeout=10000
                )
                search_box = page.locator('textarea[name="q"], input[name="q"]').first
                await search_box.fill(search_query)
                await search_box.press("Enter")

                # Wait for search results
                print("⏳ Waiting for search results...")
                await page.wait_for_selector("div#search", timeout=15000)
                await page.wait_for_load_state("networkidle")

                # Extract search result URLs
                print("📋 Extracting search result URLs...")
                search_results = await page.locator("div#search h3").all()

                urls = []
                titles = []

                for i, result in enumerate(search_results[:2]):  # First 2 results
                    try:
                        # Get the parent link
                        link_element = result.locator("xpath=ancestor::a[1]")
                        url = await link_element.get_attribute("href")
                        title = await result.inner_text()

                        if url and url.startswith("http"):
                            urls.append(url)
                            titles.append(title)
                            print(f"   {i + 1}. {title}")
                            print(f"      URL: {url}")

                    except Exception as e:
                        print(f"   ⚠️ Could not extract result {i + 1}: {e}")

                return list(zip(urls, titles))

            except Exception as e:
                print(f"❌ Error during Google search: {e}")
                return []

            finally:
                await browser.close()

    def safe_get(self, obj, key, default=None):
        """Safely get value from object, handling both dict and non-dict cases"""
        if isinstance(obj, dict):
            return obj.get(key, default)
        elif hasattr(obj, key):
            return getattr(obj, key, default)
        else:
            return default

    def safe_get_nested(self, obj, keys, default=None):
        """Safely get nested values with multiple keys"""
        current = obj
        for key in keys:
            if isinstance(current, dict):
                current = current.get(key)
            elif hasattr(current, key):
                current = getattr(current, key, None)
            else:
                return default

            if current is None:
                return default
        return current if current is not None else default

    async def scrape_urls_with_crawl4ai(self, url_title_pairs):
        """Scrape URLs using crawl4ai"""
        scraped_data = []

        async with AsyncWebCrawler(
            verbose=True, headless=self.headless, browser_type="chromium"
        ) as crawler:
            for i, (url, title) in enumerate(url_title_pairs, 2):
                print(f"\n{'=' * 80}")
                print(f"🕷️ SCRAPING WEBSITE {i}: {title}")
                print(f"🔗 URL: {url}")
                print(f"{'=' * 80}")

                
                    # Scrape with crawl4ai
                result = await crawler.arun(
                    url=url,
                    word_count_threshold=10,
                    bypass_cache=True,
                    # Enhanced scraping options
                    css_selector="main, article, .content, .post-content, body",
                    remove_overlay_elements=True,
                    simulate_user=True,
                    override_navigator=True,
                    # Wait for content to load
                    wait_for="body",
                    delay_before_return_html=2.0,
                    # JavaScript handling
                    js_code="""
                    // Scroll to load dynamic content
                    window.scrollTo(0, document.body.scrollHeight/2);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    window.scrollTo(0, 0);
                    
                    // Remove annoying overlays
                    const overlays = document.querySelectorAll('[class*="overlay"], [class*="modal"], [class*="popup"]');
                    overlays.forEach(el => el.remove());
                    """,
                    # Additional options
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    extra_headers={
                        "Accept-Language": "en-US,en;q=0.9",
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    },
                )

                if result.success:
                    try:
                        result.markdown = result.markdown.replace("\n", "  \n")
                        scraped_data.append(
                            {
                                "search_rank": i,
                                "search_title": title,
                                "url": url,
                                "success": True,
                                # "word_count": result.word_count,
                                # "links_found": result.links_found,
                                "markdown": result.markdown,
                            }
                        )
                    except Exception as e:
                        scraped_data.append(f"❌ Failed to scrape: {e}")
                else:
                    scraped_data.append(f"❌ Failed to scrape")
                
            return scraped_data

    async def save_results(self, scraped_data):
        """Save scraped results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create results directory
        results_dir = f"google_search_results_{timestamp}"
        os.makedirs(results_dir, exist_ok=True)

        # Save comprehensive JSON
        json_file = os.path.join(results_dir, "complete_results.json")
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(scraped_data, f, indent=2, ensure_ascii=False)

        # Save markdown report
        md_file = os.path.join(results_dir, "scraping_report.md")
        with open(md_file, "w", encoding="utf-8") as f:
            f.write(
                "# Google Search: 'langchain google gemini integration' - Scraping Results\n\n"
            )
            f.write(
                f"**Search performed on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            )
            f.write(f"**Query:** langchain google gemini integration\n")
            f.write(f"**Results scraped:** {len(scraped_data)}\n\n")

            f.write("## Summary\n\n")
            successful_scrapes = [r for r in scraped_data if r.get("success")]
            f.write(f"- **Total results processed:** {len(scraped_data)}\n")
            f.write(f"- **Successful scrapes:** {len(successful_scrapes)}\n")
            f.write(
                f"- **Total words scraped:** {sum(r.get('word_count', 0) for r in successful_scrapes):,}\n"
            )
            f.write(
                f"- **Total links found:** {sum(r.get('links_found', 0) for r in successful_scrapes)}\n\n"
            )

            # Individual results
            for result in scraped_data:
                rank = result.get("search_rank", "Unknown")
                f.write(
                    f"## Result #{rank}: {result.get('search_title', 'Unknown Title')}\n\n"
                )
                f.write(f"**URL:** {result['url']}\n\n")

                if result.get("success"):
                    f.write(
                        f"**Scraped Title:** {result.get('scraped_title', 'N/A')}\n"
                    )
                    f.write(f"**Description:** {result.get('description', 'N/A')}\n")
                    f.write(f"**Word Count:** {result.get('word_count', 0):,}\n")
                    f.write(f"**Links Found:** {result.get('links_found', 0)}\n")
                    f.write(f"**Images Found:** {result.get('images_found', 0)}\n\n")

                    # Content preview
                    markdown_content = result.get("markdown_content", "")
                    if markdown_content:
                        f.write("### Content Preview\n\n")
                        preview = markdown_content[:1000]
                        f.write(f"{preview}...\n\n")

                    # Save full content to separate file
                    content_file = os.path.join(results_dir, f"content_rank_{rank}.md")
                    with open(content_file, "w", encoding="utf-8") as cf:
                        cf.write(f"# {result.get('scraped_title', 'Content')}\n\n")
                        cf.write(f"**Source:** {result['url']}\n\n")
                        cf.write(markdown_content or "No content available")

                    f.write(f"**Full content saved to:** `content_rank_{rank}.md`\n\n")

                else:
                    f.write(
                        f"**❌ Scraping failed:** {result.get('error', 'Unknown error')}\n\n"
                    )

                f.write("---\n\n")

        print(f"\n📁 All results saved to directory: {results_dir}/")
        print(f"   📄 Complete data: complete_results.json")
        print(f"   📋 Summary report: scraping_report.md")
        print(f"   📝 Individual content files: content_rank_*.md")

    async def interactive_mode(self):
        """Interactive mode for testing"""
        print("🎮 INTERACTIVE MODE")
        print("This will open a visible browser so you can see what's happening")

        choice = input("Run with visible browser? (y/n): ").lower().strip()
        if choice == "y":
            self.headless = False
            self.slow_mo = 2000  # Slower for demonstration

        return await self.run_complete_workflow()


async def main():
    """Main function with options"""
    print("🔍 Google Search + Scraping Tool for 'langchain google gemini integration'")
    print("=" * 70)

    print("\nChoose mode:")
    print("1. 🤖 Automatic mode (headless browser)")
    print("2. 👁️ Interactive mode (visible browser)")
    print("3. ⚡ Quick test (visible, slower)")

    choice = input("\nEnter choice (1-3): ").strip()

    if choice == "1":
        scraper = GoogleSearchScraper(headless=True, slow_mo=500)
        results = await scraper.run_complete_workflow()
    elif choice == "2":
        scraper = GoogleSearchScraper(headless=False, slow_mo=1000)
        results = await scraper.interactive_mode()
    elif choice == "3":
        scraper = GoogleSearchScraper(headless=False, slow_mo=3000)
        results = await scraper.run_complete_workflow()
    else:
        print("Invalid choice, running automatic mode...")
        scraper = GoogleSearchScraper(headless=True, slow_mo=500)
        results = await scraper.run_complete_workflow()

    # Final summary
    if results:
        successful = len([r for r in results if r.get("success")])
        total_words = sum(r.get("word_count", 0) for r in results if r.get("success"))

        print(f"\n🎉 SCRAPING COMPLETED!")
        print(f"   ✅ Successfully scraped: {successful}/{len(results)} websites")
        print(f"   📊 Total content: {total_words:,} words")
        print(f"   🕐 Time taken: {datetime.now().strftime('%H:%M:%S')}")
    else:
        print("\n❌ No results obtained. Check your internet connection and try again.")


if __name__ == "__main__":
    # Install requirements check
    try:
        import playwright
        import crawl4ai
    except ImportError as e:
        print("❌ Missing required packages!")
        print("Install with: pip install playwright crawl4ai")
        print("Then run: playwright install chromium")
        exit(1)

    # Run the scraper
    asyncio.run(main())
